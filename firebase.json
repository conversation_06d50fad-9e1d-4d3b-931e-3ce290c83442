{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"]}], "hosting": {"site": "agnexone", "source": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "frameworksBackend": {"region": "us-central1"}}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}