import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import { logger } from 'firebase-functions/v2';

// Set user role (admin only)
export const setUserRole = onCall(async (request) => {
  const db = admin.firestore();
  const auth = admin.auth();
  
  // Check if the user is authenticated
  if (!request.auth) {
    throw new HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }

  const { uid, role } = request.data;

  // Validate input
  if (!uid || !role) {
    throw new HttpsError(
      'invalid-argument',
      'Both uid and role are required.'
    );
  }

  // Validate role
  const validRoles = ['admin', 'client', 'support'];
  if (!validRoles.includes(role)) {
    throw new HttpsError(
      'invalid-argument',
      'Invalid role specified.'
    );
  }

  try {
    // Check if the caller is an admin
    const callerToken = await auth.getUser(request.auth.uid);
    const callerClaims = callerToken.customClaims;
    
    if (!callerClaims?.role || callerClaims.role !== 'admin') {
      throw new HttpsError(
        'permission-denied',
        'Only admins can set user roles.'
      );
    }

    // Set custom claims
    await auth.setCustomUserClaims(uid, { role });

    // Update user document in Firestore
    await db.collection('users').doc(uid).update({
      role: role,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    logger.info(`Role ${role} set for user ${uid} by admin ${request.auth.uid}`);

    return { success: true, message: `Role ${role} set successfully for user ${uid}` };
  } catch (error) {
    logger.error('Error setting user role:', error);
    throw new HttpsError(
      'internal',
      'Failed to set user role.'
    );
  }
});

// Update user status - suspend or activate account (admin only)
export const updateUserStatus = onCall(async (request) => {
  const db = admin.firestore();
  const auth = admin.auth();
  
  // Check if the user is authenticated
  if (!request.auth) {
    throw new HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }

  const { uid, isActive } = request.data;

  // Validate input
  if (!uid || typeof isActive !== 'boolean') {
    throw new HttpsError(
      'invalid-argument',
      'Both uid and isActive (boolean) are required.'
    );
  }

  try {
    // Check if the caller is an admin
    const callerToken = await auth.getUser(request.auth.uid);
    const callerClaims = callerToken.customClaims;
    
    if (!callerClaims?.role || callerClaims.role !== 'admin') {
      throw new HttpsError(
        'permission-denied',
        'Only admins can update user status.'
      );
    }

    // Prevent admin from deactivating their own account
    if (uid === request.auth.uid && !isActive) {
      throw new HttpsError(
        'permission-denied',
        'You cannot deactivate your own account.'
      );
    }

    // Get the target user to verify they exist
    const targetUser = await auth.getUser(uid);
    if (!targetUser) {
      throw new HttpsError(
        'not-found',
        'Target user not found.'
      );
    }

    // Update user document in Firestore
    await db.collection('users').doc(uid).update({
      isActive: isActive,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      ...(isActive 
        ? { activatedAt: admin.firestore.FieldValue.serverTimestamp(), activatedBy: request.auth.uid }
        : { suspendedAt: admin.firestore.FieldValue.serverTimestamp(), suspendedBy: request.auth.uid }
      ),
    });

    // If suspending the user, also revoke their refresh tokens to force logout
    if (!isActive) {
      await auth.revokeRefreshTokens(uid);
    }

    // Log the action
    const action = isActive ? 'activated' : 'suspended';
    logger.info(`User ${uid} ${action} by admin ${request.auth.uid}`);

    // Create audit log
    await db.collection('audit_logs').add({
      adminId: request.auth.uid,
      adminEmail: callerToken.email || 'unknown',
      action: `user_${action}`,
      targetUserId: uid,
      targetUserEmail: targetUser.email || 'unknown',
      details: {
        isActive,
        reason: request.data.reason || 'No reason provided',
        oldStatus: !isActive, // Assuming opposite status
      },
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });

    return { 
      success: true, 
      message: `User account ${action} successfully`,
      uid,
      isActive 
    };
  } catch (error) {
    logger.error('Error updating user status:', error);
    
    // Re-throw HttpsError instances
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError(
      'internal',
      'Failed to update user status.'
    );
  }
});

// Create user (admin only)
export const createUser = onCall(async (request) => {
  const db = admin.firestore();
  const auth = admin.auth();
  
  // Check if the user is authenticated
  if (!request.auth) {
    logger.error('createUser called without authentication');
    throw new HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }

  logger.info(`createUser called by user: ${request.auth.uid}`);

  const { email, password, displayName, role } = request.data;

  // Validate input
  if (!email || !password || !displayName || !role) {
    logger.error('createUser called with missing required fields', { email: !!email, password: !!password, displayName: !!displayName, role: !!role });
    throw new HttpsError(
      'invalid-argument',
      'email, password, displayName, and role are required.'
    );
  }

  // Validate role
  const validRoles = ['admin', 'client', 'support'];
  if (!validRoles.includes(role)) {
    logger.error(`createUser called with invalid role: ${role}`);
    throw new HttpsError(
      'invalid-argument',
      'Invalid role specified.'
    );
  }

  try {
    // Check if the caller is an admin
    const callerToken = await auth.getUser(request.auth.uid);
    const callerClaims = callerToken.customClaims;
    
    logger.info(`Caller claims:`, { claims: callerClaims });
    
    if (!callerClaims?.role || callerClaims.role !== 'admin') {
      logger.error(`Non-admin user ${request.auth.uid} attempted to create user`);
      throw new HttpsError(
        'permission-denied',
        'Only admins can create users.'
      );
    }

    logger.info(`Creating user: ${email} with role: ${role}`);

    // Create user
    const userRecord = await auth.createUser({
      email,
      password,
      displayName,
      emailVerified: false,
    });

    logger.info(`Firebase Auth user created: ${userRecord.uid}`);

    // Set custom claims
    await auth.setCustomUserClaims(userRecord.uid, { role });
    logger.info(`Custom claims set for user: ${userRecord.uid}`);

    // Create user document in Firestore
    await db.collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      email: email,
      displayName: displayName,
      role: role,
      isActive: true,
      emailVerified: false,
      profileComplete: false,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      createdBy: request.auth.uid,
    });

    logger.info(`Firestore user document created for: ${userRecord.uid}`);

    // Create audit log
    await db.collection('audit_logs').add({
      adminId: request.auth.uid,
      adminEmail: callerToken.email || 'unknown',
      action: 'user_created',
      targetUserId: userRecord.uid,
      targetUserEmail: email,
      details: {
        role,
        displayName,
        isActive: true,
      },
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });

    logger.info(`User ${userRecord.uid} created successfully with role ${role} by ${request.auth.uid}`);

    return { 
      success: true, 
      message: 'User created successfully',
      uid: userRecord.uid,
      email,
      displayName,
      role 
    };
  } catch (error) {
    logger.error('Error creating user:', error);
    
    // Re-throw HttpsError instances
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError(
      'internal',
      'Failed to create user.'
    );
  }
}); 