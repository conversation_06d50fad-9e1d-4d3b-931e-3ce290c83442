import { onCall } from 'firebase-functions/v2/https';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import { logger } from 'firebase-functions/v2';

interface DeletePaymentRequest {
  paymentId: string;
  reason?: string;
}

export const deletePayment = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const { paymentId, reason } = request.data as DeletePaymentRequest;
  const db = getFirestore();
  const storage = getStorage();
  const userId = request.auth.uid;

  try {
    // Validate inputs
    if (!paymentId) {
      throw new Error('Payment ID is required');
    }

    // Get payment document to check for associated invoice
    const paymentRef = db.collection('payments').doc(paymentId);
    const paymentDoc = await paymentRef.get();
    
    if (!paymentDoc.exists) {
      throw new Error('Payment not found');
    }

    const paymentData = paymentDoc.data();
    const invoiceId = paymentData?.invoiceId;
    
    logger.info(`Deleting payment ${paymentId}${invoiceId ? ` with associated invoice ${invoiceId}` : ''}`);

    // If payment has an associated invoice, clean it up
    if (invoiceId) {
      await deleteInvoiceAndPDF(db, storage, invoiceId);
    }

    // Delete the payment document
    await paymentRef.delete();

    // Log activity
    const activityRef = db.collection('activities').doc();
    await activityRef.set({
      type: 'payment_deleted',
      userId,
      targetId: paymentId,
      targetType: 'payment',
      metadata: {
        paymentId,
        amount: paymentData?.amount,
        currency: paymentData?.currency,
        invoiceId: invoiceId || null,
        reason: reason || 'Payment deleted'
      },
      timestamp: new Date()
    });

    logger.info(`Successfully deleted payment ${paymentId}${invoiceId ? ' and associated invoice' : ''}`);

    return {
      success: true,
      paymentId,
      deletedInvoice: !!invoiceId,
      invoiceId: invoiceId || null
    };

  } catch (error) {
    logger.error('Error deleting payment:', error);
    throw error;
  }
});

// Helper function to delete invoice and its PDF from Firebase Storage
async function deleteInvoiceAndPDF(
  db: FirebaseFirestore.Firestore,
  storage: any,
  invoiceId: string
): Promise<void> {
  try {
    // Get invoice document to get invoice number for PDF file path
    const invoiceDoc = await db.collection('invoices').doc(invoiceId).get();
    
    if (invoiceDoc.exists) {
      const invoiceData = invoiceDoc.data();
      const invoiceNumber = invoiceData?.invoiceNumber;

      // Delete PDF from Firebase Storage if it exists
      if (invoiceNumber) {
        try {
          const bucket = storage.bucket();
          const fileName = `invoices/${invoiceId}/invoice-${invoiceNumber}.pdf`;
          const file = bucket.file(fileName);
          
          // Check if file exists before attempting to delete
          const [exists] = await file.exists();
          if (exists) {
            await file.delete();
            logger.info(`Deleted PDF file: ${fileName}`);
          } else {
            logger.info(`PDF file not found: ${fileName}`);
          }
        } catch (storageError) {
          // Log warning but don't fail the entire operation if PDF deletion fails
          logger.warn(`Failed to delete PDF for invoice ${invoiceId}:`, storageError);
        }
      }

      // Delete the invoice document from Firestore
      await db.collection('invoices').doc(invoiceId).delete();
      logger.info(`Deleted invoice document: ${invoiceId}`);
    }
  } catch (error) {
    // Log warning but don't fail the payment deletion if invoice cleanup fails
    logger.warn(`Failed to clean up invoice ${invoiceId}:`, error);
    throw error; // Re-throw in Cloud Function for better error handling
  }
}
