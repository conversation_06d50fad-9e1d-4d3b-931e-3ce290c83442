import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, Timestamp, FieldValue } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions/v2';

interface UpdatePaymentStatusRequest {
  paymentId: string;
  newStatus: 'pending' | 'paid' | 'overdue' | 'cancelled';
  reason?: string;
}

export const updatePaymentStatus = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const { paymentId, newStatus, reason } = request.data as UpdatePaymentStatusRequest;
  const db = getFirestore();
  const userId = request.auth.uid;

  try {
    // Get payment document
    const paymentRef = db.collection('payments').doc(paymentId);
    const paymentDoc = await paymentRef.get();
    
    if (!paymentDoc.exists) {
      throw new Error('Payment not found');
    }

    const payment = paymentDoc.data();
    const oldStatus = payment?.status;
    const projectId = payment?.projectId;

    // Validate status transition
    if (oldStatus === newStatus) {
      logger.info(`Payment ${paymentId} status is already ${newStatus}, skipping update`);
      return { 
        success: true, 
        paymentId, 
        oldStatus, 
        newStatus,
        message: `Payment status is already ${newStatus}`,
        updatedAt: payment?.updatedAt || Timestamp.now()
      };
    }

    // Prepare update data
    const updateData: any = {
      status: newStatus,
      updatedAt: Timestamp.now()
    };

    // Set paidAt timestamp when marking as paid
    if (newStatus === 'paid' && oldStatus !== 'paid') {
      updateData.paidAt = Timestamp.now();
    } else if (newStatus !== 'paid' && payment?.paidAt) {
      updateData.paidAt = FieldValue.delete();
    }

    // Update payment in a transaction
    await db.runTransaction(async (transaction) => {
      // Update payment
      transaction.update(paymentRef, updateData);

      // Log status change
      const logRef = db.collection('paymentStatusLogs').doc();
      transaction.set(logRef, {
        paymentId,
        projectId,
        oldStatus,
        newStatus,
        reason: reason || 'Status updated',
        updatedBy: userId,
        updatedAt: Timestamp.now()
      });

      // Log activity
      const activityRef = db.collection('activities').doc();
      transaction.set(activityRef, {
        type: 'payment_status_change',
        userId,
        targetId: paymentId,
        targetType: 'payment',
        metadata: {
          paymentId,
          projectId,
          oldStatus,
          newStatus,
          amount: payment?.amount,
          currency: payment?.currency
        },
        timestamp: Timestamp.now()
      });
    });

    logger.info(`Payment status updated: ${paymentId} from ${oldStatus} to ${newStatus}`);

    return { 
      success: true, 
      paymentId, 
      oldStatus, 
      newStatus,
      updatedAt: updateData.updatedAt
    };
  } catch (error) {
    logger.error('Error updating payment status:', error);
    throw error;
  }
}); 