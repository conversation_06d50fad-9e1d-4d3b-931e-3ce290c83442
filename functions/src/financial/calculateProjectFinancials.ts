import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions/v2';

export const calculateProjectFinancials = onCall(async (request) => {
  // Verify authentication
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const { projectId, forceRecalculate = false } = request.data;
  const db = getFirestore();

  try {
    // Check if recent calculation exists (unless forced)
    if (!forceRecalculate) {
      const existingSummary = await db
        .collection('projectFinancialSummaries')
        .doc(projectId)
        .get();
      
      if (existingSummary.exists) {
        const data = existingSummary.data();
        const lastCalc = data?.lastCalculatedAt?.toDate();
        const oneMinuteAgo = new Date(Date.now() - 60 * 1000); // 1 minute cache
        
        if (lastCalc && lastCalc > oneMinuteAgo) {
          return { cached: true, summary: data };
        }
      }
    }

    // Get project data (only for totalCost and currency)
    const projectDoc = await db.collection('projects').doc(projectId).get();
    if (!projectDoc.exists) {
      throw new Error('Project not found');
    }

    const project = projectDoc.data();
    const totalCost = project?.totalCost || 0;

    // Calculate payment totals by status and type
    const paymentTotals = await calculatePaymentTotals(db, projectId);
    const paymentBreakdown = await calculatePaymentBreakdown(db, projectId);

    // Calculate progress percentages
    const paymentProgress = totalCost > 0 ? (paymentTotals.totalPaid / totalCost) * 100 : 0;

    const summary = {
      projectId,
      totalCost,
      totalPaid: paymentTotals.totalPaid,
      regularPayments: paymentBreakdown.regularPayments,
      changeRequestPayments: paymentBreakdown.changeRequestPayments,
      recurringPayments: paymentBreakdown.recurringPayments,
      totalPending: paymentTotals.totalPending,
      totalOverdue: paymentTotals.totalOverdue,
      totalCancelled: paymentTotals.totalCancelled,
      paymentProgress: Math.round(paymentProgress * 100) / 100,
      baseProjectCost: paymentBreakdown.baseProjectCost,
      totalChangeRequestCost: paymentBreakdown.totalChangeRequestCost,
      paymentsCount: paymentTotals.paymentsCount,
      lastPaymentDate: paymentTotals.lastPaymentDate,
      lastCalculatedAt: Timestamp.now(),
      autoCalculated: true
    };

    // Store calculated summary (single source of truth)
    await db
      .collection('projectFinancialSummaries')
      .doc(projectId)
      .set(summary);

    logger.info(`Financial summary calculated for project ${projectId}`, summary);
    
    return { cached: false, summary };
  } catch (error) {
    logger.error('Error calculating project financials:', error);
    throw error;
  }
});

async function calculatePaymentBreakdown(
  db: FirebaseFirestore.Firestore,
  projectId: string
): Promise<{
  regularPayments: number;
  changeRequestPayments: number;
  recurringPayments: number;
  baseProjectCost: number;
  totalChangeRequestCost: number;
}> {
  // Get regular payments
  const paymentsSnapshot = await db
    .collection('payments')
    .where('projectId', '==', projectId)
    .where('status', '==', 'paid')
    .get();

  // Get recurring payments
  const recurringPaymentsSnapshot = await db
    .collection('recurringPayments')
    .where('projectId', '==', projectId)
    .where('status', '==', 'paid')
    .get();

  let regularPayments = 0;
  let changeRequestPayments = 0;
  let recurringPayments = 0;
  let totalChangeRequestCost = 0;

  // Process regular payments
  paymentsSnapshot.forEach(doc => {
    const payment = doc.data();
    const amount = payment.amount || 0;
    const paymentType = payment.paymentType || 'regular';

    switch (paymentType) {
      case 'regular':
        regularPayments += amount;
        break;
      case 'change_request':
        changeRequestPayments += amount;
        // For change requests, the payment amount is also added to project cost
        totalChangeRequestCost += amount;
        break;
      case 'recurring':
        recurringPayments += amount;
        break;
      default:
        regularPayments += amount; // Fallback for unknown types
    }
  });

  // Process recurring payments
  recurringPaymentsSnapshot.forEach(doc => {
    const payment = doc.data();
    const amount = payment.amount || 0;
    recurringPayments += amount;
  });

  // Get base project cost from project document
  const projectDoc = await db.collection('projects').doc(projectId).get();
  const project = projectDoc.data();
  const baseProjectCost = project?.baseProjectCost || project?.totalCost || 0;

  return {
    regularPayments,
    changeRequestPayments,
    recurringPayments,
    baseProjectCost,
    totalChangeRequestCost
  };
}

async function calculatePaymentTotals(
  db: FirebaseFirestore.Firestore,
  projectId: string
): Promise<{
  totalPaid: number;
  totalPending: number;
  totalOverdue: number;
  totalCancelled: number;
  paymentsCount: number;
  lastPaymentDate: any;
}> {
  // Get regular payments
  const paymentsSnapshot = await db
    .collection('payments')
    .where('projectId', '==', projectId)
    .get();

  // Get recurring payments
  const recurringPaymentsSnapshot = await db
    .collection('recurringPayments')
    .where('projectId', '==', projectId)
    .get();

  let totalPaid = 0;
  let totalPending = 0;
  let totalOverdue = 0;
  let totalCancelled = 0;
  let lastPaymentDate: any = null;

  // Process regular payments
  paymentsSnapshot.forEach(doc => {
    const payment = doc.data();
    const amount = payment.amount || 0;

    switch (payment.status) {
      case 'paid':
        totalPaid += amount;
        if (payment.paidAt && (!lastPaymentDate || payment.paidAt > lastPaymentDate)) {
          lastPaymentDate = payment.paidAt;
        }
        break;
      case 'pending':
        totalPending += amount;
        break;
      case 'overdue':
        totalOverdue += amount;
        break;
      case 'cancelled':
        totalCancelled += amount;
        break;
    }
  });

  // Process recurring payments
  recurringPaymentsSnapshot.forEach(doc => {
    const payment = doc.data();
    const amount = payment.amount || 0;

    switch (payment.status) {
      case 'paid':
        totalPaid += amount;
        if (payment.paidAt && (!lastPaymentDate || payment.paidAt > lastPaymentDate)) {
          lastPaymentDate = payment.paidAt;
        }
        break;
      case 'pending':
        totalPending += amount;
        break;
      case 'overdue':
        totalOverdue += amount;
        break;
      case 'cancelled':
        totalCancelled += amount;
        break;
    }
  });

  return {
    totalPaid,
    totalPending,
    totalOverdue,
    totalCancelled,
    paymentsCount: paymentsSnapshot.size + recurringPaymentsSnapshot.size,
    lastPaymentDate
  };
}