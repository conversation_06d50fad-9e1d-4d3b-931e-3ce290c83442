import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions/v2';

interface GetNotificationsRequest {
  filters?: {
    type?: string;
    isRead?: boolean;
    recipientId?: string;
  };
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface MarkNotificationAsReadRequest {
  notificationId: string;
}

interface CreateNotificationRequest {
  type: string;
  title: string;
  message: string;
  recipientId: string;
  relatedEntityId?: string;
  relatedEntityType?: string;
}

// Get notifications with filtering and pagination
export const getNotifications = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as GetNotificationsRequest;
  const db = getFirestore();
  const userId = request.auth.uid;

  try {
    let query: any = db.collection('paymentNotifications');

    // Users can only see their own notifications, admins can see all
    const userToken = request.auth.token;
    if (userToken.role !== 'admin') {
      query = query.where('recipientId', '==', userId);
    } else if (data.filters?.recipientId) {
      query = query.where('recipientId', '==', data.filters.recipientId);
    }

    // Apply filters
    if (data.filters) {
      if (data.filters.type) {
        query = query.where('type', '==', data.filters.type);
      }
      if (data.filters.isRead !== undefined) {
        query = query.where('isRead', '==', data.filters.isRead);
      }
    }

    // Apply sorting
    const sortBy = data.sortBy || 'createdAt';
    const sortOrder = data.sortOrder || 'desc';
    query = query.orderBy(sortBy, sortOrder);

    // Apply pagination
    const limit = data.limit || 50;
    const offset = data.offset || 0;

    if (offset > 0) {
      const offsetQuery = db.collection('paymentNotifications')
        .orderBy(sortBy, sortOrder)
        .limit(offset);
      const offsetSnapshot = await offsetQuery.get();
      if (!offsetSnapshot.empty) {
        const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
        query = query.startAfter(lastDoc);
      }
    }

    query = query.limit(limit);

    const snapshot = await query.get();

    // Get total count for pagination
    let totalQuery: any = db.collection('paymentNotifications');
    if (userToken.role !== 'admin') {
      totalQuery = totalQuery.where('recipientId', '==', userId);
    }
    const totalSnapshot = await totalQuery.get();
    const total = totalSnapshot.size;

    const notifications = snapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data()
    }));

    return {
      notifications,
      total,
      limit,
      offset,
      hasMore: offset + limit < total
    };

  } catch (error) {
    logger.error('Error getting notifications:', error);
    throw error;
  }
});

// Mark notification as read
export const markNotificationAsRead = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as MarkNotificationAsReadRequest;
  const db = getFirestore();
  const userId = request.auth.uid;

  try {
    const notificationRef = db.collection('paymentNotifications').doc(data.notificationId);
    const notificationDoc = await notificationRef.get();

    if (!notificationDoc.exists) {
      throw new Error('Notification not found');
    }

    const notificationData = notificationDoc.data();
    
    // Check if user owns this notification or is admin
    const userToken = request.auth.token;
    if (userToken.role !== 'admin' && notificationData?.recipientId !== userId) {
      throw new Error('Not authorized to modify this notification');
    }

    await notificationRef.update({
      isRead: true,
      readAt: FieldValue.serverTimestamp(),
    });

    logger.info(`Notification marked as read: ${data.notificationId}`);
    return { success: true };

  } catch (error) {
    logger.error('Error marking notification as read:', error);
    throw error;
  }
});

// Mark all notifications as read for a user
export const markAllNotificationsAsRead = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const db = getFirestore();
  const userId = request.auth.uid;

  try {
    const unreadNotificationsSnapshot = await db
      .collection('paymentNotifications')
      .where('recipientId', '==', userId)
      .where('isRead', '==', false)
      .get();

    if (unreadNotificationsSnapshot.empty) {
      return { success: true, updated: 0 };
    }

    const batch = db.batch();
    unreadNotificationsSnapshot.docs.forEach(doc => {
      batch.update(doc.ref, {
        isRead: true,
        readAt: FieldValue.serverTimestamp(),
      });
    });

    await batch.commit();

    logger.info(`All notifications marked as read for user: ${userId}`);
    return { success: true, updated: unreadNotificationsSnapshot.size };

  } catch (error) {
    logger.error('Error marking all notifications as read:', error);
    throw error;
  }
});

// Create notification (admin only)
export const createNotification = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  // Check if user has admin role
  const userToken = request.auth.token;
  if (userToken.role !== 'admin') {
    throw new Error('Only admins can create notifications');
  }

  const data = request.data as CreateNotificationRequest;
  const db = getFirestore();

  try {
    // Verify recipient exists
    const recipientDoc = await db.collection('users').doc(data.recipientId).get();
    if (!recipientDoc.exists) {
      throw new Error('Recipient not found');
    }

    const notificationId = generateNotificationId();
    const recipientData = recipientDoc.data();
    
    const notification = {
      id: notificationId,
      type: data.type,
      title: data.title,
      message: data.message,
      recipientId: data.recipientId,
      recipientEmail: recipientData?.email || '',
      relatedEntityId: data.relatedEntityId || null,
      relatedEntityType: data.relatedEntityType || null,
      isRead: false,
      sentAt: null,
      createdAt: FieldValue.serverTimestamp(),
    };

    await db.collection('paymentNotifications').doc(notificationId).set(notification);

    logger.info(`Notification created: ${notificationId}`);
    return { success: true, notificationId };

  } catch (error) {
    logger.error('Error creating notification:', error);
    throw error;
  }
});

// Delete notification
export const deleteNotification = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const { notificationId } = request.data;
  const db = getFirestore();
  const userId = request.auth.uid;

  try {
    const notificationRef = db.collection('paymentNotifications').doc(notificationId);
    const notificationDoc = await notificationRef.get();

    if (!notificationDoc.exists) {
      throw new Error('Notification not found');
    }

    const notificationData = notificationDoc.data();
    
    // Check if user owns this notification or is admin
    const userToken = request.auth.token;
    if (userToken.role !== 'admin' && notificationData?.recipientId !== userId) {
      throw new Error('Not authorized to delete this notification');
    }

    await notificationRef.delete();

    logger.info(`Notification deleted: ${notificationId}`);
    return { success: true };

  } catch (error) {
    logger.error('Error deleting notification:', error);
    throw error;
  }
});

// Get notification statistics (admin only)
export const getNotificationStatistics = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  // Check if user has admin role
  const userToken = request.auth.token;
  if (userToken.role !== 'admin') {
    throw new Error('Only admins can view notification statistics');
  }

  const db = getFirestore();

  try {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get various statistics
    const [
      totalNotifications,
      unreadNotifications,
      notificationsLast24h,
      notificationsLast7d,
      paymentDueNotifications,
      overdueNotifications
    ] = await Promise.all([
      db.collection('paymentNotifications').get(),
      db.collection('paymentNotifications').where('isRead', '==', false).get(),
      db.collection('paymentNotifications').where('createdAt', '>=', last24Hours).get(),
      db.collection('paymentNotifications').where('createdAt', '>=', last7Days).get(),
      db.collection('paymentNotifications').where('type', '==', 'payment_due').get(),
      db.collection('paymentNotifications').where('type', '==', 'payment_overdue').get()
    ]);

    return {
      totalNotifications: totalNotifications.size,
      unreadNotifications: unreadNotifications.size,
      notificationsLast24Hours: notificationsLast24h.size,
      notificationsLast7Days: notificationsLast7d.size,
      paymentDueNotifications: paymentDueNotifications.size,
      overdueNotifications: overdueNotifications.size,
    };

  } catch (error) {
    logger.error('Error getting notification statistics:', error);
    throw error;
  }
});

// Helper function to generate notification ID
function generateNotificationId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `ONE-NOT-${timestamp}-${random}`;
}
