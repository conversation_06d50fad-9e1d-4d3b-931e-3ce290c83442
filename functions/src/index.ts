import * as admin from 'firebase-admin';
import { setUserRole, createUser, updateUserStatus } from './auth';
import { onUserDocumentCreated, onPaymentStatusChange } from './triggers';
import { onPaymentUpdate } from './triggers/onPaymentUpdate';
import { calculateProjectFinancials, updatePaymentStatus, updateProjectCostOnChangeRequest, deletePayment } from './financial';
import { generateInvoice, regenerateInvoice } from './invoice/generateInvoice';
import { getInvoices, updateInvoiceStatus, generateConsolidatedInvoice } from './invoice/invoiceManagement';
import { downloadInvoice } from './invoice/downloadInvoice';
import {
  createMaintenanceContract,
  updateMaintenanceContract,
  deleteMaintenanceContract,
  createRecurringPayment,
  markRecurringPaymentAsPaid,
  uploadSupportingDocument,
  deleteSupportingDocument,
  markRecurringPaymentAsOverdue,
  processScheduledPayments,
  triggerPaymentProcessing,
  getMaintenanceContracts,
  getRecurringPayments,
  getRecurringPaymentSummary,
  getUpcomingPayments,
  getOverduePayments
} from './recurring';
import {
  onPaymentNotificationCreated,
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  createNotification,
  deleteNotification,
  getNotificationStatistics
} from './notifications';
import {
  uploadFile,
  getFileDownloadUrl,
  deleteFile
} from './storage';

// Initialize Firebase Admin SDK
admin.initializeApp();

// Export only the required functions
export {
  setUserRole,
  createUser,
  updateUserStatus,
  onUserDocumentCreated,
  onPaymentStatusChange,
  onPaymentUpdate,
  calculateProjectFinancials,
  updatePaymentStatus,
  updateProjectCostOnChangeRequest,
  deletePayment,
  // Invoice functions - manual only
  generateInvoice,
  regenerateInvoice,
  getInvoices,
  updateInvoiceStatus,
  generateConsolidatedInvoice,
  downloadInvoice,
  // Recurring payment functions
  createMaintenanceContract,
  updateMaintenanceContract,
  deleteMaintenanceContract,
  createRecurringPayment,
  markRecurringPaymentAsPaid,
  uploadSupportingDocument,
  deleteSupportingDocument,
  markRecurringPaymentAsOverdue,
  processScheduledPayments,
  triggerPaymentProcessing,
  getMaintenanceContracts,
  getRecurringPayments,
  getRecurringPaymentSummary,
  getUpcomingPayments,
  getOverduePayments,
  // Notification functions
  onPaymentNotificationCreated,
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  createNotification,
  deleteNotification,
  getNotificationStatistics,
  // File upload functions
  uploadFile,
  getFileDownloadUrl,
  deleteFile
};