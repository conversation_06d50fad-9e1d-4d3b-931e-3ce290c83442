interface InvoiceData {
  invoice: any;
  project: any;
  client: any;
  payments: any[];
}

interface CompanyDetails {
  name: string;
  tagline: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  gstin: string;
}

export function generateProfessionalInvoiceHTML(data: InvoiceData): string {
  const { invoice, project, client, payments } = data;

  const companyDetails: CompanyDetails = {
    name: 'AGNEX STUDIO',
    tagline: 'Digital Excellence in Every Pixel',
    address: 'Telangana, India',
    phone: '+91 91888 78022',
    email: '<EMAIL>',
    website: 'agnex.in',
    gstin: '32COLPG6895J1ZE'
  };

  const formatDate = (timestamp: any): string => {
    if (!timestamp) return 'N/A';
    try {
      let date: Date;
      if (timestamp && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp && timestamp.seconds) {
        date = new Date(timestamp.seconds * 1000);
      } else {
        date = new Date(timestamp);
      }
      return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: '2-digit'
      });
    } catch {
      return 'N/A';
    }
  };

  const formatCurrency = (amount: number, currency = 'INR'): string => {
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount);
  };

  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'paid': return '#059669';
      case 'sent': return '#2563eb';
      case 'overdue': return '#dc2626';
      case 'draft': return '#7c3aed';
      default: return '#6b7280';
    }
  };

  const getStatusBadge = (status: string): string => {
    const color = getStatusColor(status);
    return `<span style="background: ${color}; color: white; padding: 4px 12px; border-radius: 20px; font-size: 11px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">${status}</span>`;
  };

  const totalAmount = payments.reduce((sum, payment) => sum + payment.amount, 0);

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${invoice.invoiceNumber}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.4;
            color: #1f2937;
            background: #ffffff;
            font-size: 14px;
        }

        .invoice-container {
            width: 210mm;
            max-width: 210mm;
            min-height: 297mm;
            max-height: 297mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
            position: relative;
            overflow: hidden;
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2563eb;
        }

        .company-info {
            flex: 1;
        }

        .company-name {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
            letter-spacing: -0.5px;
        }

        .company-tagline {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 12px;
            font-weight: 400;
        }

        .company-details {
            font-size: 11px;
            line-height: 1.5;
            color: #4b5563;
        }

        .company-details div {
            margin-bottom: 2px;
        }

        .invoice-title {
            text-align: right;
            flex: 0 0 auto;
        }

        .invoice-title h1 {
            font-size: 32px;
            font-weight: 700;
            color: #2563eb;
            margin-bottom: 4px;
            letter-spacing: -1px;
        }

        .invoice-number {
            font-size: 14px;
            font-weight: 600;
            color: #6b7280;
        }

        /* Invoice Info Grid */
        .invoice-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 25px;
        }

        .info-section {
            background: #f8fafc;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
        }

        .info-section h3 {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            color: #2563eb;
            margin-bottom: 12px;
            letter-spacing: 0.5px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .info-label {
            color: #6b7280;
            font-weight: 500;
        }

        .info-value {
            color: #1f2937;
            font-weight: 600;
        }

        .client-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .client-contact {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        /* Payment Items Table */
        .payment-items {
            margin-bottom: 20px;
        }

        .payment-items h2 {
            font-size: 14px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .items-table thead {
            background: #f9fafb;
        }

        .items-table th {
            padding: 10px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .items-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 12px;
            vertical-align: top;
        }

        .items-table tbody tr:last-child td {
            border-bottom: none;
        }

        .amount-cell {
            font-weight: 600;
            color: #1f2937;
        }

        .date-cell {
            color: #6b7280;
        }

        /* Totals Section */
        .totals-section {
            margin-bottom: 20px;
        }

        .totals-container {
            max-width: 300px;
            margin-left: auto;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 16px;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            font-size: 13px;
        }

        .total-row.final-total {
            border-top: 2px solid #2563eb;
            margin-top: 8px;
            padding-top: 8px;
            font-weight: 700;
            font-size: 16px;
            color: #2563eb;
        }

        .total-label {
            color: #6b7280;
            font-weight: 500;
        }

        .total-value {
            font-weight: 600;
            color: #1f2937;
        }

        /* Notes Section */
        .notes-section {
            margin-bottom: 20px;
        }

        .notes-section h3 {
            font-size: 12px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .notes-content {
            background: #f9fafb;
            padding: 12px;
            border-radius: 4px;
            border-left: 3px solid #2563eb;
            font-size: 11px;
            line-height: 1.5;
            color: #4b5563;
        }

        /* Footer */
        .footer {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 16px;
        }

        .footer-section h4 {
            font-size: 11px;
            font-weight: 600;
            margin-bottom: 6px;
            color: #374151;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .footer-section p {
            font-size: 10px;
            line-height: 1.4;
            color: #6b7280;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 12px;
            border-top: 1px solid #f3f4f6;
            font-size: 10px;
            color: #9ca3af;
        }

        .footer-brand {
            font-weight: 600;
            color: #2563eb;
        }

        /* Print Styles */
        @media print {
            .invoice-container {
                margin: 0;
                padding: 15mm;
                width: 100%;
                max-width: none;
                min-height: 100vh;
                max-height: none;
            }

            body {
                background: white;
                font-size: 12px;
            }
        }

        /* Page break control */
        .page-break {
            page-break-before: always;
        }

        .no-break {
            page-break-inside: avoid;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="company-info">
                <div class="company-name">${companyDetails.name}</div>
                <div class="company-tagline">${companyDetails.tagline}</div>
                <div class="company-details">
                    <div>${companyDetails.address}</div>
                    <div>${companyDetails.phone} • ${companyDetails.email}</div>
                    <div>${companyDetails.website}</div>
                    <div>GSTIN: ${companyDetails.gstin}</div>
                </div>
            </div>
            <div class="invoice-title">
                <h1>INVOICE</h1>
                <div class="invoice-number">#${invoice.invoiceNumber}</div>
            </div>
        </div>

        <!-- Invoice Information Grid -->
        <div class="invoice-info">
            <div class="info-section">
                <h3>Invoice Details</h3>
                <div class="info-row">
                    <span class="info-label">Issue Date:</span>
                    <span class="info-value">${formatDate(invoice.issueDate)}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Due Date:</span>
                    <span class="info-value">${formatDate(invoice.dueDate)}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Status:</span>
                    <span class="info-value">${getStatusBadge(invoice.status)}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Currency:</span>
                    <span class="info-value">${invoice.currency}</span>
                </div>
            </div>

            <div class="info-section">
                <h3>Bill To</h3>
                <div class="client-name">${client.name || client.displayName || 'Client Name'}</div>
                <div class="client-contact">
                    <div>${client.email || '<EMAIL>'}</div>
                    ${client.phone ? `<div>${client.phone}</div>` : ''}
                </div>
                <div style="margin-top: 12px;">
                    <div class="info-row">
                        <span class="info-label">Project:</span>
                        <span class="info-value">${project.name || 'N/A'}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Type:</span>
                        <span class="info-value">${invoice.type === 'individual' ? 'Individual' : 'Consolidated'}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Items -->
        <div class="payment-items no-break">
            <h2>Payment Details</h2>
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 40%;">Description</th>
                        <th style="width: 20%;">Date</th>
                        <th style="width: 20%;">Type</th>
                        <th style="width: 20%; text-align: right;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    ${payments.map((payment, index) => `
                        <tr>
                            <td>
                                <div style="font-weight: 500; margin-bottom: 2px;">
                                    ${payment.description || `Payment #${index + 1}`}
                                </div>
                                ${payment.linkedTicketId ? `<div style="font-size: 10px; color: #6b7280;">Ticket: ${payment.linkedTicketId}</div>` : ''}
                            </td>
                            <td class="date-cell">${formatDate(payment.paidAt || payment.createdAt)}</td>
                            <td>
                                <span style="background: ${payment.paymentType === 'change_request' ? '#f59e0b' : '#3b82f6'}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; font-weight: 500;">
                                    ${payment.paymentType === 'change_request' ? 'Change' : 'Regular'}
                                </span>
                            </td>
                            <td class="amount-cell" style="text-align: right;">${formatCurrency(payment.amount, payment.currency)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>

        <!-- Totals Section -->
        <div class="totals-section no-break">
            <div class="totals-container">
                <div class="total-row">
                    <span class="total-label">Subtotal:</span>
                    <span class="total-value">${formatCurrency(totalAmount, invoice.currency)}</span>
                </div>
                <div class="total-row final-total">
                    <span class="total-label">TOTAL AMOUNT:</span>
                    <span class="total-value">${formatCurrency(invoice.totalAmount, invoice.currency)}</span>
                </div>
            </div>
        </div>

        <!-- Notes Section -->
        ${invoice.notes ? `
        <div class="notes-section">
            <h3>Notes</h3>
            <div class="notes-content">
                ${invoice.notes}
            </div>
        </div>
        ` : ''}

        <!-- Footer -->
        <div class="footer">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Terms & Conditions</h4>
                    <p>
                        ${invoice.terms || 'Payment is due within 30 days of invoice date. Late payments may incur additional charges. This invoice is digitally generated and does not require a physical signature.'}
                    </p>
                </div>
                <div class="footer-section">
                    <h4>Thank You</h4>
                    <p>
                        Thank you for choosing Agnex Studio for your project needs. We appreciate your business and look forward to working with you again.
                    </p>
                </div>
            </div>
            <div class="footer-bottom">
                <div>Generated on ${new Date().toLocaleDateString('en-IN')} • ${new Date().toLocaleTimeString('en-IN')}</div>
                <div>This is a digitally generated invoice by <span class="footer-brand">AGNEX STUDIO</span></div>
            </div>
        </div>
    </div>
</body>
</html>
  `;
}