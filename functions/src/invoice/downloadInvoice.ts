import { onCall } from 'firebase-functions/v2/https';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import { logger } from 'firebase-functions/v2';
import { generateProfessionalInvoiceHTML } from './professionalInvoiceTemplate';

// Use require for packages that may not have proper TypeScript declarations
const puppeteer = require('puppeteer-core');
const chromium = require('@sparticuz/chromium');

interface DownloadInvoiceRequest {
  invoiceId: string;
}

export const downloadInvoice = onCall({
  memory: '2GiB',
  timeoutSeconds: 300,
}, async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as DownloadInvoiceRequest;
  const db = getFirestore();
  const storage = getStorage();

  try {
    // Validate inputs
    if (!data.invoiceId) {
      throw new Error('Invoice ID is required');
    }

    // Get invoice details
    const invoiceDoc = await db.collection('invoices').doc(data.invoiceId).get();

    if (!invoiceDoc.exists) {
      throw new Error('Invoice not found');
    }

    const invoice = { id: invoiceDoc.id, ...invoiceDoc.data() } as any;

    // Check if PDF already exists in storage
    const bucket = storage.bucket();
    const fileName = `invoices/${data.invoiceId}/invoice-${invoice.invoiceNumber}.pdf`;
    const file = bucket.file(fileName);

    try {
      const [exists] = await file.exists();
      
      if (exists) {
        // Download the file and return as base64
        const [fileBuffer] = await file.download();
        const base64Data = fileBuffer.toString('base64');
        
        logger.info(`Retrieved existing PDF for invoice ${invoice.invoiceNumber}`);
        return { 
          pdfData: base64Data,
          filename: `invoice-${invoice.invoiceNumber}.pdf`,
          contentType: 'application/pdf'
        };
      }
    } catch (error) {
      logger.warn('Error checking existing PDF:', error);
    }

    // Generate new PDF
    logger.info(`Generating PDF for invoice ${invoice.invoiceNumber}`);
    
    // Get additional data for PDF generation
    const [projectDoc, clientDoc, paymentsSnapshot] = await Promise.all([
      db.collection('projects').doc(invoice.projectId).get(),
      db.collection('users').doc(invoice.clientId).get(),
      db.collection('payments').where('invoiceId', '==', data.invoiceId).get()
    ]);

    const project = projectDoc.exists ? projectDoc.data() : {};
    const client = clientDoc.exists ? clientDoc.data() : {};
    const payments = paymentsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Generate PDF buffer using HTML-to-PDF
    const pdfBuffer = await generateProfessionalInvoicePDF({
      invoice,
      project,
      client,
      payments
    });

    // Upload PDF to Storage for caching
    await file.save(pdfBuffer, {
      metadata: {
        contentType: 'application/pdf',
        metadata: {
          invoiceId: data.invoiceId,
          invoiceNumber: invoice.invoiceNumber,
          generatedAt: new Date().toISOString()
        }
      }
    });

    // Return PDF as base64
    const base64Data = pdfBuffer.toString('base64');

    logger.info(`Generated and uploaded PDF for invoice ${invoice.invoiceNumber}`);

    return { 
      pdfData: base64Data,
      filename: `invoice-${invoice.invoiceNumber}.pdf`,
      contentType: 'application/pdf'
    };

  } catch (error) {
    logger.error('Error downloading invoice:', error);
    throw error;
  }
});

async function generateProfessionalInvoicePDF(data: {
  invoice: any;
  project: any;
  client: any;
  payments: any[];
}): Promise<Buffer> {
  let browser = null;
  
  try {
    // Configure Chromium for cloud functions
    const executablePath = await chromium.executablePath();
    
    // Launch browser with optimized settings for cloud functions
    browser = await puppeteer.launch({
      args: [
        ...chromium.args,
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
      ],
      defaultViewport: chromium.defaultViewport,
      executablePath,
      headless: chromium.headless,
      ignoreHTTPSErrors: true,
    });

    const page = await browser.newPage();

    // Generate the HTML content
    const htmlContent = generateProfessionalInvoiceHTML(data);

    // Set the HTML content
    await page.setContent(htmlContent, {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Generate PDF with A4 specifications
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      preferCSSPageSize: true,
      margin: {
        top: '0mm',
        right: '0mm',
        bottom: '0mm',
        left: '0mm'
      },
      displayHeaderFooter: false,
    });

    logger.info('PDF generated successfully using HTML-to-PDF conversion');
    return pdfBuffer;

  } catch (error) {
    logger.error('Error generating PDF:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}
