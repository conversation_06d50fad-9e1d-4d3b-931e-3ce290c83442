import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions/v2';

interface GenerateInvoiceRequest {
  type: 'individual' | 'consolidated';
  projectId: string;
  clientId: string;
  paymentIds: string[];
  description?: string;
  notes?: string;
  dueDate?: Date;
}

interface RegenerateInvoiceRequest {
  invoiceId: string;
  description?: string;
  notes?: string;
  dueDate?: Date;
}

export const generateInvoice = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as GenerateInvoiceRequest;
  const db = getFirestore();
  const userId = request.auth.uid;

  try {
    // Validate inputs
    if (!data.projectId || !data.clientId || !data.paymentIds?.length) {
      throw new Error('Missing required fields');
    }

    // Get project details
    const projectDoc = await db.collection('projects').doc(data.projectId).get();

    if (!projectDoc.exists) {
      throw new Error('Project not found');
    }

    const project = projectDoc.data();

    // Get payment details
    const paymentDocs = await Promise.all(
      data.paymentIds.map(id => db.collection('payments').doc(id).get())
    );

    const payments = paymentDocs
      .filter(doc => doc.exists)
      .map(doc => ({ id: doc.id, ...doc.data() })) as any[];

    if (payments.length === 0) {
      throw new Error('No valid payments found');
    }

    // Validate all payments are paid
    const unpaidPayments = payments.filter((p: any) => p.status !== 'paid');
    if (unpaidPayments.length > 0) {
      throw new Error('All payments must be paid to generate invoice');
    }

    // Calculate invoice totals
    const amount = payments.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0);
    const totalAmount = amount;

    // Generate invoice number
    const invoiceNumber = await generateInvoiceNumber(db);

    // Create invoice document
    const invoiceData: any = {
      invoiceNumber,
      type: data.type,
      projectId: data.projectId,
      clientId: data.clientId,
      
      // Financial details
      amount,
      currency: (payments[0] as any).currency || 'INR',
      totalAmount,
      
      // Invoice metadata
      status: 'sent' as const,
      issueDate: Timestamp.now(),
      dueDate: data.dueDate ? Timestamp.fromDate(new Date(data.dueDate)) : Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
      
      // Content
      description: data.description || generateDefaultDescription(data.type, project?.name, payments.length),
      
      // Related entities
      paymentIds: data.paymentIds,
      linkedTicketIds: payments.flatMap((p: any) => p.linkedTicketId ? [p.linkedTicketId] : []),
      
      // Generation metadata
      generatedBy: userId,
      generatedAt: Timestamp.now(),
      
      // Tracking
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    // Only add notes if it's not undefined/null to avoid Firestore validation errors
    if (data.notes !== undefined && data.notes !== null && data.notes !== '') {
      invoiceData.notes = data.notes;
    }

    // Save invoice
    const invoiceRef = db.collection('invoices').doc();
    await invoiceRef.set(invoiceData);

    // Update payments with invoice reference
    const batch = db.batch();
    data.paymentIds.forEach(paymentId => {
      const paymentRef = db.collection('payments').doc(paymentId);
      batch.update(paymentRef, {
        invoiceId: invoiceRef.id,
        invoicedAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    });
    await batch.commit();

    logger.info(`Invoice ${invoiceNumber} generated for ${data.paymentIds.length} payments`);

    return {
      success: true,
      invoiceId: invoiceRef.id,
      invoiceNumber,
      totalAmount,
      paymentCount: data.paymentIds.length
    };

  } catch (error) {
    logger.error('Error generating invoice:', error);
    throw error;
  }
});

async function generateInvoiceNumber(db: FirebaseFirestore.Firestore): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `INV-${year}-`;
  
  // Get the latest invoice number for this year
  const invoicesSnapshot = await db
    .collection('invoices')
    .where('invoiceNumber', '>=', prefix)
    .where('invoiceNumber', '<', `INV-${year + 1}-`)
    .orderBy('invoiceNumber', 'desc')
    .limit(1)
    .get();

  let nextNumber = 1;
  if (!invoicesSnapshot.empty) {
    const lastInvoiceNumber = invoicesSnapshot.docs[0].data().invoiceNumber;
    const lastNumber = parseInt(lastInvoiceNumber.split('-')[2]) || 0;
    nextNumber = lastNumber + 1;
  }

  return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
}

// Regenerate existing invoice with updated payment data
export const regenerateInvoice = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as RegenerateInvoiceRequest;
  const db = getFirestore();
  const userId = request.auth.uid;

  try {
    logger.info(`Regenerating invoice ${data.invoiceId} by user ${userId}`);

    // Get existing invoice
    const invoiceRef = db.collection('invoices').doc(data.invoiceId);
    const invoiceDoc = await invoiceRef.get();

    if (!invoiceDoc.exists) {
      throw new Error('Invoice not found');
    }

    const existingInvoice = invoiceDoc.data();
    if (!existingInvoice) {
      throw new Error('Invalid invoice data');
    }

    // Get all payments associated with this invoice
    const paymentsSnapshot = await db
      .collection('payments')
      .where('invoiceId', '==', data.invoiceId)
      .get();

    if (paymentsSnapshot.empty) {
      throw new Error('No payments found for this invoice');
    }

    const payments = paymentsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as any[];

    // Validate all payments are still paid
    const unpaidPayments = payments.filter((p: any) => p.status !== 'paid');
    if (unpaidPayments.length > 0) {
      throw new Error('All payments must be paid to regenerate invoice');
    }

    // Get project and client data
    const [projectDoc, clientDoc] = await Promise.all([
      db.collection('projects').doc(existingInvoice.projectId).get(),
      db.collection('users').doc(existingInvoice.clientId).get()
    ]);

    const project = projectDoc.exists ? projectDoc.data() : null;
    const client = clientDoc.exists ? clientDoc.data() : null;

    if (!project || !client) {
      throw new Error('Project or client not found');
    }

    // Recalculate invoice totals with current payment data
    const amount = payments.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0);
    const totalAmount = amount;

    // Prepare updated invoice data
    const updatedInvoiceData: any = {
      // Keep original invoice metadata
      invoiceNumber: existingInvoice.invoiceNumber,
      type: existingInvoice.type,
      projectId: existingInvoice.projectId,
      clientId: existingInvoice.clientId,

      // Update financial details with current payment data
      amount,
      currency: payments[0].currency || existingInvoice.currency || 'INR',
      totalAmount,

      // Keep original dates but allow due date update
      status: existingInvoice.status,
      issueDate: existingInvoice.issueDate,
      dueDate: data.dueDate ? Timestamp.fromDate(new Date(data.dueDate)) : existingInvoice.dueDate,
      paidDate: existingInvoice.paidDate,

      // Update content if provided, otherwise keep existing
      description: data.description || existingInvoice.description,
      notes: data.notes !== undefined ? (data.notes || null) : existingInvoice.notes,

      // Update related entities with current payment data
      paymentIds: payments.map((p: any) => p.id),
      linkedTicketIds: payments.flatMap((p: any) => p.linkedTicketId ? [p.linkedTicketId] : []),

      // Keep original generation metadata but add regeneration info
      generatedBy: existingInvoice.generatedBy,
      generatedAt: existingInvoice.generatedAt,

      // Add regeneration tracking
      lastRegeneratedBy: userId,
      lastRegeneratedAt: Timestamp.now(),
      regenerationCount: (existingInvoice.regenerationCount || 0) + 1,

      // Update tracking
      updatedAt: Timestamp.now()
    };

    // Update invoice
    await invoiceRef.update(updatedInvoiceData);

    // Log the regeneration activity
    const activityRef = db.collection('activities').doc();
    await activityRef.set({
      type: 'invoice_regenerated',
      userId,
      targetId: data.invoiceId,
      targetType: 'invoice',
      metadata: {
        invoiceId: data.invoiceId,
        invoiceNumber: existingInvoice.invoiceNumber,
        projectId: existingInvoice.projectId,
        paymentCount: payments.length,
        totalAmount,
        regenerationCount: updatedInvoiceData.regenerationCount
      },
      timestamp: Timestamp.now()
    });

    logger.info(`Successfully regenerated invoice ${data.invoiceId}`);

    return {
      success: true,
      invoiceId: data.invoiceId,
      invoiceNumber: existingInvoice.invoiceNumber,
      totalAmount,
      paymentCount: payments.length,
      regenerationCount: updatedInvoiceData.regenerationCount
    };

  } catch (error) {
    logger.error('Error regenerating invoice:', error);
    throw error;
  }
});

function generateDefaultDescription(type: string, projectName: string, paymentCount: number): string {
  if (type === 'individual') {
    return `Invoice for payment - ${projectName}`;
  } else {
    return `Consolidated invoice for ${paymentCount} payments - ${projectName}`;
  }
}
