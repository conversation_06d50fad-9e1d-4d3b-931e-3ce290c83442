import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions/v2';

// Get invoices with filtering and pagination
export const getInvoices = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const filters = request.data.filters || {};
  const limit = request.data.limit || 50;
  const db = getFirestore();

  try {
    let query: any = db.collection('invoices');

    // Apply filters
    if (filters.projectId) {
      query = query.where('projectId', '==', filters.projectId);
    }
    if (filters.clientId) {
      query = query.where('clientId', '==', filters.clientId);
    }
    if (filters.status) {
      query = query.where('status', '==', filters.status);
    }
    if (filters.type) {
      query = query.where('type', '==', filters.type);
    }

    // Apply sorting and limit
    query = query.orderBy('createdAt', 'desc').limit(limit);

    const snapshot = await query.get();
    const invoices = [];

    for (const doc of snapshot.docs) {
      const data = doc.data();
      
      // Get project name
      let projectName = 'Unknown Project';
      try {
        const projectDoc = await db.collection('projects').doc(data.projectId).get();
        if (projectDoc.exists) {
          projectName = projectDoc.data()?.name || 'Unknown Project';
        }
      } catch (error) {
        logger.warn(`Failed to get project name for ${data.projectId}`);
      }

      // Get client name
      let clientName = 'Unknown Client';
      try {
        const clientDoc = await db.collection('users').doc(data.clientId).get();
        if (clientDoc.exists) {
          const client = clientDoc.data();
          clientName = client?.displayName || client?.name || client?.email || 'Unknown Client';
        }
      } catch (error) {
        logger.warn(`Failed to get client name for ${data.clientId}`);
      }

      invoices.push({
        id: doc.id,
        ...data,
        projectName,
        clientName
      });
    }

    return {
      invoices,
      total: invoices.length,
      hasMore: invoices.length === limit
    };

  } catch (error) {
    logger.error('Error getting invoices:', error);
    throw error;
  }
});

// Update invoice status
export const updateInvoiceStatus = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const { invoiceId, newStatus, reason } = request.data;
  const db = getFirestore();
  const userId = request.auth.uid;

  try {
    const invoiceRef = db.collection('invoices').doc(invoiceId);
    const invoiceDoc = await invoiceRef.get();

    if (!invoiceDoc.exists) {
      throw new Error('Invoice not found');
    }

    const invoice = invoiceDoc.data();
    const oldStatus = invoice?.status;

    if (oldStatus === newStatus) {
      throw new Error('Status is already set');
    }

    const updateData: any = {
      status: newStatus,
      updatedAt: Timestamp.now()
    };

    // Set paidDate when marking as paid
    if (newStatus === 'paid' && oldStatus !== 'paid') {
      updateData.paidDate = Timestamp.now();
    } else if (newStatus !== 'paid' && invoice?.paidDate) {
      updateData.paidDate = null;
    }

    // Update invoice in a transaction
    await db.runTransaction(async (transaction) => {
      transaction.update(invoiceRef, updateData);

      // Log status change
      const logRef = db.collection('invoiceStatusLogs').doc();
      transaction.set(logRef, {
        invoiceId,
        oldStatus,
        newStatus,
        reason: reason || 'Status updated',
        updatedBy: userId,
        updatedAt: Timestamp.now()
      });

      // Log activity
      const activityRef = db.collection('activities').doc();
      transaction.set(activityRef, {
        type: 'invoice_status_change',
        userId,
        targetId: invoiceId,
        targetType: 'invoice',
        metadata: {
          invoiceId,
          invoiceNumber: invoice?.invoiceNumber,
          oldStatus,
          newStatus,
          totalAmount: invoice?.totalAmount
        },
        timestamp: Timestamp.now()
      });
    });

    logger.info(`Invoice status updated: ${invoiceId} from ${oldStatus} to ${newStatus}`);

    return {
      success: true,
      invoiceId,
      oldStatus,
      newStatus,
      updatedAt: updateData.updatedAt
    };

  } catch (error) {
    logger.error('Error updating invoice status:', error);
    throw error;
  }
});

// Generate consolidated invoice for multiple payments
export const generateConsolidatedInvoice = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const { projectId, clientId, paymentIds, description, notes, dueDate } = request.data;
  const db = getFirestore();
  const userId = request.auth.uid;

  try {
    // Validate inputs
    if (!projectId || !clientId || !paymentIds?.length) {
      throw new Error('Missing required fields');
    }

    // Get project details
    const projectDoc = await db.collection('projects').doc(projectId).get();
    if (!projectDoc.exists) {
      throw new Error('Project not found');
    }

    const project = projectDoc.data();

    // Get payment details
    const paymentDocs = await Promise.all(
      paymentIds.map((id: string) => db.collection('payments').doc(id).get())
    );

    const payments = paymentDocs
      .filter(doc => doc.exists)
      .map(doc => ({ id: doc.id, ...doc.data() })) as any[];

    if (payments.length === 0) {
      throw new Error('No valid payments found');
    }

    // Validate all payments are paid and belong to the project
    const invalidPayments = payments.filter((p: any) => 
      p.status !== 'paid' || p.projectId !== projectId || p.clientId !== clientId
    );
    
    if (invalidPayments.length > 0) {
      throw new Error('All payments must be paid and belong to the same project and client');
    }

    // Check if any payment already has an invoice
    const invoicedPayments = payments.filter((p: any) => p.invoiceId);
    if (invoicedPayments.length > 0) {
      throw new Error('Some payments are already invoiced');
    }

    // Calculate totals
    const amount = payments.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0);
    const totalAmount = amount;

    // Generate invoice number
    const invoiceNumber = await generateInvoiceNumber(db);

    // Create consolidated invoice
    const invoiceData = {
      invoiceNumber,
      type: 'consolidated' as const,
      projectId,
      clientId,
      
      // Financial details
      amount,
      currency: payments[0].currency || 'INR',
      totalAmount,
      
      // Invoice metadata
      status: 'sent' as const,
      issueDate: Timestamp.now(),
      dueDate: dueDate ? Timestamp.fromDate(new Date(dueDate)) : Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
      
      // Content
      description: description || `Consolidated invoice for ${payments.length} payments - ${project?.name}`,
      notes,
      
      // Related entities
      paymentIds,
      linkedTicketIds: payments.flatMap((p: any) => p.linkedTicketId ? [p.linkedTicketId] : []),
      
      // Generation metadata
      generatedBy: userId,
      generatedAt: Timestamp.now(),
      autoGenerated: false,
      
      // Tracking
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    // Save invoice and update payments in a transaction
    const invoiceRef = db.collection('invoices').doc();
    await db.runTransaction(async (transaction) => {
      // Create invoice
      transaction.set(invoiceRef, invoiceData);

      // Update all payments with invoice reference
      paymentIds.forEach((paymentId: string) => {
        const paymentRef = db.collection('payments').doc(paymentId);
        transaction.update(paymentRef, {
          invoiceId: invoiceRef.id,
          invoicedAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        });
      });
    });

    logger.info(`Consolidated invoice ${invoiceNumber} generated for ${paymentIds.length} payments`);

    return {
      success: true,
      invoiceId: invoiceRef.id,
      invoiceNumber,
      totalAmount,
      paymentCount: paymentIds.length
    };

  } catch (error) {
    logger.error('Error generating consolidated invoice:', error);
    throw error;
  }
});

async function generateInvoiceNumber(db: FirebaseFirestore.Firestore): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `INV-${year}-`;
  
  // Get the latest invoice number for this year
  const invoicesSnapshot = await db
    .collection('invoices')
    .where('invoiceNumber', '>=', prefix)
    .where('invoiceNumber', '<', `INV-${year + 1}-`)
    .orderBy('invoiceNumber', 'desc')
    .limit(1)
    .get();

  let nextNumber = 1;
  if (!invoicesSnapshot.empty) {
    const lastInvoiceNumber = invoicesSnapshot.docs[0].data().invoiceNumber;
    const lastNumber = parseInt(lastInvoiceNumber.split('-')[2]) || 0;
    nextNumber = lastNumber + 1;
  }

  return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
}
