import { onDocumentUpdated } from 'firebase-functions/v2/firestore';
import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions/v2';

export const onPaymentUpdate = onDocumentUpdated(
  'payments/{paymentId}',
  async (event) => {
    const { paymentId } = event.params;
    const beforeData = event.data?.before.data();
    const afterData = event.data?.after.data();

    if (!beforeData || !afterData) {
      logger.warn(`Missing payment data for ${paymentId}`);
      return;
    }

    const db = getFirestore();

    try {
      // Check if payment has an associated invoice
      const invoiceId = afterData.invoiceId;
      if (!invoiceId) {
        logger.info(`Payment ${paymentId} has no associated invoice, skipping regeneration`);
        return;
      }

      // Check if relevant payment data changed (amount, currency, description, notes)
      const relevantFieldsChanged = (
        beforeData.amount !== afterData.amount ||
        beforeData.currency !== afterData.currency ||
        beforeData.description !== afterData.description ||
        beforeData.notes !== afterData.notes
      );

      if (!relevantFieldsChanged) {
        logger.info(`No relevant fields changed for payment ${paymentId}, skipping invoice regeneration`);
        return;
      }

      // Only regenerate if payment is still paid
      if (afterData.status !== 'paid') {
        logger.info(`Payment ${paymentId} is not paid, skipping invoice regeneration`);
        return;
      }

      logger.info(`Payment ${paymentId} data changed, triggering invoice regeneration for invoice ${invoiceId}`);

      // Get the invoice to check if it exists
      const invoiceRef = db.collection('invoices').doc(invoiceId);
      const invoiceDoc = await invoiceRef.get();

      if (!invoiceDoc.exists) {
        logger.warn(`Invoice ${invoiceId} not found for payment ${paymentId}`);
        return;
      }

      const existingInvoice = invoiceDoc.data();
      if (!existingInvoice) {
        logger.warn(`Invalid invoice data for ${invoiceId}`);
        return;
      }

      // Get all payments associated with this invoice
      const paymentsSnapshot = await db
        .collection('payments')
        .where('invoiceId', '==', invoiceId)
        .get();

      if (paymentsSnapshot.empty) {
        logger.warn(`No payments found for invoice ${invoiceId}`);
        return;
      }

      const payments = paymentsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as any[];

      // Validate all payments are still paid
      const unpaidPayments = payments.filter((p: any) => p.status !== 'paid');
      if (unpaidPayments.length > 0) {
        logger.warn(`Some payments for invoice ${invoiceId} are not paid, skipping regeneration`);
        return;
      }

      // Recalculate invoice totals with current payment data
      const amount = payments.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0);
      const totalAmount = amount;

      // Prepare updated invoice data
      const updatedInvoiceData: any = {
        // Update financial details with current payment data
        amount,
        currency: payments[0].currency || existingInvoice.currency || 'INR',
        totalAmount,
        
        // Update related entities with current payment data
        paymentIds: payments.map((p: any) => p.id),
        linkedTicketIds: payments.flatMap((p: any) => p.linkedTicketId ? [p.linkedTicketId] : []),
        
        // Add auto-regeneration tracking
        lastAutoRegeneratedAt: Timestamp.now(),
        autoRegenerationCount: (existingInvoice.autoRegenerationCount || 0) + 1,
        
        // Update tracking
        updatedAt: Timestamp.now()
      };

      // Update invoice
      await invoiceRef.update(updatedInvoiceData);

      // Log the auto-regeneration activity
      const activityRef = db.collection('activities').doc();
      await activityRef.set({
        type: 'invoice_auto_regenerated',
        userId: 'system',
        targetId: invoiceId,
        targetType: 'invoice',
        metadata: {
          invoiceId,
          invoiceNumber: existingInvoice.invoiceNumber,
          projectId: existingInvoice.projectId,
          paymentId,
          paymentCount: payments.length,
          totalAmount,
          autoRegenerationCount: updatedInvoiceData.autoRegenerationCount,
          trigger: 'payment_update'
        },
        timestamp: Timestamp.now()
      });

      logger.info(`Successfully auto-regenerated invoice ${invoiceId} due to payment ${paymentId} update`);

    } catch (error) {
      logger.error(`Error auto-regenerating invoice for payment ${paymentId}:`, error);
      // Don't throw - we don't want to break the payment update
    }
  }
);
