import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, Timestamp, FieldValue } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions/v2';

interface CreateMaintenanceContractRequest {
  projectId: string;
  clientId: string;
  name: string;
  description?: string;
  amount: number;
  currency: string;
  frequency: 'monthly' | 'quarterly' | 'yearly';
  startDate: string; // ISO date string
  endDate?: string; // ISO date string
  autoGenerateInvoices: boolean;
  reminderDaysBefore: number;
  gracePeriodDays: number;
  notes?: string;
}

interface UpdateMaintenanceContractRequest {
  contractId: string;
  updates: Partial<CreateMaintenanceContractRequest>;
}

interface CreateRecurringPaymentRequest {
  maintenanceContractId: string;
  dueDate: string; // ISO date string
  description?: string;
  notes?: string;
}

// Removed unused interface

// Create maintenance contract
export const createMaintenanceContract = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as CreateMaintenanceContractRequest;
  const db = getFirestore();

  try {
    // Validate project and client exist
    const [projectDoc, clientDoc] = await Promise.all([
      db.collection('projects').doc(data.projectId).get(),
      db.collection('users').doc(data.clientId).get()
    ]);

    if (!projectDoc.exists) {
      throw new Error('Project not found');
    }

    if (!clientDoc.exists) {
      throw new Error('Client not found');
    }

    // Generate contract ID
    const contractId = generateMaintenanceContractId();
    
    // Calculate next due date based on frequency and start date
    const startDate = Timestamp.fromDate(new Date(data.startDate));
    const nextDueDate = calculateNextDueDate(startDate, data.frequency);
    
    const contract = {
      id: contractId,
      projectId: data.projectId,
      clientId: data.clientId,
      name: data.name,
      description: data.description || null,
      amount: data.amount,
      currency: data.currency,
      frequency: data.frequency,
      status: 'active' as const,
      startDate,
      endDate: data.endDate ? Timestamp.fromDate(new Date(data.endDate)) : null,
      nextDueDate,
      lastPaymentDate: null,
      totalPaymentsMade: 0,
      totalAmountPaid: 0,
      autoGenerateInvoices: data.autoGenerateInvoices,
      reminderDaysBefore: data.reminderDaysBefore,
      gracePeriodDays: data.gracePeriodDays,
      notes: data.notes || null,
      createdBy: request.auth.uid,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
    };

    await db.collection('maintenanceContracts').doc(contractId).set(contract);

    // Create the first recurring payment
    await createInitialRecurringPayment(db, contractId, request.auth.uid);

    logger.info(`Maintenance contract created: ${contractId}`);
    return { success: true, contractId };

  } catch (error) {
    logger.error('Error creating maintenance contract:', error);
    throw error;
  }
});

// Update maintenance contract
export const updateMaintenanceContract = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as UpdateMaintenanceContractRequest;
  const db = getFirestore();

  try {
    const contractRef = db.collection('maintenanceContracts').doc(data.contractId);
    const contractDoc = await contractRef.get();

    if (!contractDoc.exists) {
      throw new Error('Maintenance contract not found');
    }

    const updates: any = {
      ...data.updates,
      updatedAt: FieldValue.serverTimestamp(),
    };

    // Convert date strings to Timestamps if provided
    if (data.updates.startDate) {
      updates.startDate = Timestamp.fromDate(new Date(data.updates.startDate));
    }
    if (data.updates.endDate) {
      updates.endDate = Timestamp.fromDate(new Date(data.updates.endDate));
    }

    // Recalculate next due date if frequency or start date changed
    if (data.updates.frequency || data.updates.startDate) {
      const contractData = contractDoc.data();
      const startDate = updates.startDate || contractData?.startDate;
      const frequency = updates.frequency || contractData?.frequency;
      updates.nextDueDate = calculateNextDueDate(startDate, frequency);
    }

    await contractRef.update(updates);

    logger.info(`Maintenance contract updated: ${data.contractId}`);
    return { success: true };

  } catch (error) {
    logger.error('Error updating maintenance contract:', error);
    throw error;
  }
});

// Delete maintenance contract
export const deleteMaintenanceContract = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const { contractId } = request.data;
  const db = getFirestore();

  try {
    // Check if contract exists
    const contractDoc = await db.collection('maintenanceContracts').doc(contractId).get();
    if (!contractDoc.exists) {
      throw new Error('Maintenance contract not found');
    }

    // Delete all related recurring payments
    const paymentsSnapshot = await db
      .collection('recurringPayments')
      .where('maintenanceContractId', '==', contractId)
      .get();

    const batch = db.batch();
    
    // Delete contract
    batch.delete(db.collection('maintenanceContracts').doc(contractId));
    
    // Delete all related payments
    paymentsSnapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();

    logger.info(`Maintenance contract deleted: ${contractId}`);
    return { success: true };

  } catch (error) {
    logger.error('Error deleting maintenance contract:', error);
    throw error;
  }
});

// Create recurring payment
export const createRecurringPayment = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as CreateRecurringPaymentRequest;
  const db = getFirestore();

  try {
    // Get contract details
    const contractDoc = await db.collection('maintenanceContracts').doc(data.maintenanceContractId).get();
    if (!contractDoc.exists) {
      throw new Error('Maintenance contract not found');
    }

    const contract = contractDoc.data();
    const paymentId = generateRecurringPaymentId();
    
    const payment = {
      id: paymentId,
      maintenanceContractId: data.maintenanceContractId,
      projectId: contract?.projectId,
      clientId: contract?.clientId,
      amount: contract?.amount,
      currency: contract?.currency,
      status: 'pending' as const,
      dueDate: Timestamp.fromDate(new Date(data.dueDate)),
      paidAt: null,
      overdueAt: null,
      description: data.description || null,
      notes: data.notes || null,
      supportingDocuments: [],
      invoiceId: null,
      invoicedAt: null,
      reminderSentAt: null,
      overdueNotificationSentAt: null,
      createdBy: request.auth.uid,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
    };

    await db.collection('recurringPayments').doc(paymentId).set(payment);

    logger.info(`Recurring payment created: ${paymentId}`);
    return { success: true, paymentId };

  } catch (error) {
    logger.error('Error creating recurring payment:', error);
    throw error;
  }
});

// Helper functions
function generateMaintenanceContractId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `ONE-MCT-${timestamp}-${random}`;
}

function generateRecurringPaymentId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `ONE-RPY-${timestamp}-${random}`;
}

function calculateNextDueDate(startDate: Timestamp, frequency: string): Timestamp {
  const date = startDate.toDate();
  
  switch (frequency) {
    case 'monthly':
      date.setMonth(date.getMonth() + 1);
      break;
    case 'quarterly':
      date.setMonth(date.getMonth() + 3);
      break;
    case 'yearly':
      date.setFullYear(date.getFullYear() + 1);
      break;
    default:
      throw new Error(`Invalid frequency: ${frequency}`);
  }
  
  return Timestamp.fromDate(date);
}

async function createInitialRecurringPayment(db: any, contractId: string, createdBy: string) {
  const contractDoc = await db.collection('maintenanceContracts').doc(contractId).get();
  const contract = contractDoc.data();
  
  const paymentId = generateRecurringPaymentId();
  
  const payment = {
    id: paymentId,
    maintenanceContractId: contractId,
    projectId: contract.projectId,
    clientId: contract.clientId,
    amount: contract.amount,
    currency: contract.currency,
    status: 'pending' as const,
    dueDate: contract.nextDueDate,
    paidAt: null,
    overdueAt: null,
    description: `${contract.frequency} maintenance payment for ${contract.name}`,
    notes: null,
    supportingDocuments: [],
    invoiceId: null,
    invoicedAt: null,
    reminderSentAt: null,
    overdueNotificationSentAt: null,
    createdBy,
    createdAt: FieldValue.serverTimestamp(),
    updatedAt: FieldValue.serverTimestamp(),
  };

  await db.collection('recurringPayments').doc(paymentId).set(payment);
}
