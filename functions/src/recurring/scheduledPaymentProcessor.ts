import { onSchedule } from 'firebase-functions/v2/scheduler';
import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, Timestamp, FieldValue } from 'firebase-admin/firestore';
import { logger } from 'firebase-functions/v2';

interface ProcessScheduledPaymentsResponse {
  processed: number;
  created: number;
  markedOverdue: number;
  errors: string[];
}

// Scheduled function to run daily at 9 AM UTC
export const processScheduledPayments = onSchedule('0 9 * * *', async (event) => {
  const db = getFirestore();
  const today = new Date();
  const todayTimestamp = Timestamp.fromDate(today);
  
  logger.info('Starting scheduled payment processing...');
  
  try {
    const result = await processPayments(db, todayTimestamp);
    
    logger.info('Scheduled payment processing completed', result);
    
    // Store processing log
    await db.collection('scheduledProcessingLogs').add({
      type: 'payment_processing',
      result,
      processedAt: FieldValue.serverTimestamp(),
    });
    
  } catch (error) {
    logger.error('Error in scheduled payment processing:', error);
    
    // Store error log
    await db.collection('scheduledProcessingLogs').add({
      type: 'payment_processing',
      error: error instanceof Error ? error.message : 'Unknown error',
      processedAt: FieldValue.serverTimestamp(),
    });
  }
});

// Manual trigger for payment processing (for testing/admin use)
export const triggerPaymentProcessing = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  // Check if user has admin role
  const userToken = request.auth.token;
  if (userToken.role !== 'admin') {
    throw new Error('Only admins can trigger payment processing');
  }

  const db = getFirestore();
  const today = new Date();
  const todayTimestamp = Timestamp.fromDate(today);
  
  logger.info('Manual payment processing triggered by:', request.auth.uid);
  
  try {
    const result = await processPayments(db, todayTimestamp, request.data?.dryRun || false);
    
    logger.info('Manual payment processing completed', result);
    return result;
    
  } catch (error) {
    logger.error('Error in manual payment processing:', error);
    throw error;
  }
});

// Core payment processing logic
async function processPayments(
  db: any, 
  currentDate: Timestamp, 
  dryRun: boolean = false
): Promise<ProcessScheduledPaymentsResponse> {
  const result: ProcessScheduledPaymentsResponse = {
    processed: 0,
    created: 0,
    markedOverdue: 0,
    errors: []
  };

  try {
    // 1. Process overdue payments
    await processOverduePayments(db, currentDate, result, dryRun);
    
    // 2. Create new payments for active contracts
    await createNewPayments(db, currentDate, result, dryRun);
    
    // 3. Send reminders for upcoming payments
    await sendPaymentReminders(db, currentDate, result, dryRun);
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    result.errors.push(errorMessage);
    logger.error('Error in payment processing:', error);
  }

  return result;
}

// Process overdue payments
async function processOverduePayments(
  db: any, 
  currentDate: Timestamp, 
  result: ProcessScheduledPaymentsResponse, 
  dryRun: boolean
) {
  // Find payments that are past due and not yet marked as overdue
  const overduePaymentsQuery = await db
    .collection('recurringPayments')
    .where('status', '==', 'pending')
    .where('dueDate', '<', currentDate)
    .get();

  for (const paymentDoc of overduePaymentsQuery.docs) {
    try {
      const paymentData = paymentDoc.data();
      const paymentId = paymentDoc.id;
      
      // Get contract to check grace period
      const contractDoc = await db
        .collection('maintenanceContracts')
        .doc(paymentData.maintenanceContractId)
        .get();
      
      if (!contractDoc.exists) {
        result.errors.push(`Contract not found for payment ${paymentId}`);
        continue;
      }
      
      const contract = contractDoc.data();
      const gracePeriodDays = contract.gracePeriodDays || 0;
      
      // Calculate if grace period has passed
      const dueDate = paymentData.dueDate.toDate();
      const gracePeriodEnd = new Date(dueDate);
      gracePeriodEnd.setDate(gracePeriodEnd.getDate() + gracePeriodDays);
      
      if (currentDate.toDate() > gracePeriodEnd) {
        if (!dryRun) {
          await db.collection('recurringPayments').doc(paymentId).update({
            status: 'overdue',
            overdueAt: FieldValue.serverTimestamp(),
            updatedAt: FieldValue.serverTimestamp(),
          });
          
          // Create overdue notification
          await createOverdueNotification(db, paymentId, paymentData);
        }
        
        result.markedOverdue++;
        logger.info(`Payment marked as overdue: ${paymentId}`);
      }
      
      result.processed++;
      
    } catch (error) {
      const errorMessage = `Error processing overdue payment ${paymentDoc.id}: ${error}`;
      result.errors.push(errorMessage);
      logger.error(errorMessage);
    }
  }
}

// Create new payments for contracts that need them
async function createNewPayments(
  db: any, 
  currentDate: Timestamp, 
  result: ProcessScheduledPaymentsResponse, 
  dryRun: boolean
) {
  // Find active contracts where nextDueDate is today or in the past
  const contractsQuery = await db
    .collection('maintenanceContracts')
    .where('status', '==', 'active')
    .where('nextDueDate', '<=', currentDate)
    .get();

  for (const contractDoc of contractsQuery.docs) {
    try {
      const contract = contractDoc.data();
      const contractId = contractDoc.id;
      
      // Check if payment already exists for this due date
      const existingPaymentQuery = await db
        .collection('recurringPayments')
        .where('maintenanceContractId', '==', contractId)
        .where('dueDate', '==', contract.nextDueDate)
        .get();
      
      if (!existingPaymentQuery.empty) {
        continue; // Payment already exists for this due date
      }
      
      // Check if contract has expired
      if (contract.endDate && currentDate.toDate() > contract.endDate.toDate()) {
        if (!dryRun) {
          await db.collection('maintenanceContracts').doc(contractId).update({
            status: 'expired',
            updatedAt: FieldValue.serverTimestamp(),
          });
        }
        continue;
      }
      
      if (!dryRun) {
        // Create new payment
        const paymentId = generateRecurringPaymentId();
        
        const payment = {
          id: paymentId,
          maintenanceContractId: contractId,
          projectId: contract.projectId,
          clientId: contract.clientId,
          amount: contract.amount,
          currency: contract.currency,
          status: 'pending' as const,
          dueDate: contract.nextDueDate,
          paidAt: null,
          overdueAt: null,
          description: `${contract.frequency} maintenance payment for ${contract.name}`,
          notes: null,
          supportingDocuments: [],
          invoiceId: null,
          invoicedAt: null,
          reminderSentAt: null,
          overdueNotificationSentAt: null,
          createdBy: 'system',
          createdAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        };

        await db.collection('recurringPayments').doc(paymentId).set(payment);
        
        // Update contract's next due date
        const nextDueDate = calculateNextDueDate(contract.nextDueDate, contract.frequency);
        await db.collection('maintenanceContracts').doc(contractId).update({
          nextDueDate,
          updatedAt: FieldValue.serverTimestamp(),
        });
        
        logger.info(`New recurring payment created: ${paymentId}`);
      }
      
      result.created++;
      result.processed++;
      
    } catch (error) {
      const errorMessage = `Error creating payment for contract ${contractDoc.id}: ${error}`;
      result.errors.push(errorMessage);
      logger.error(errorMessage);
    }
  }
}

// Send payment reminders
async function sendPaymentReminders(
  db: any, 
  currentDate: Timestamp, 
  result: ProcessScheduledPaymentsResponse, 
  dryRun: boolean
) {
  // Find payments that need reminders sent
  const reminderDate = new Date(currentDate.toDate());
  reminderDate.setDate(reminderDate.getDate() + 3); // 3 days from now
  const reminderTimestamp = Timestamp.fromDate(reminderDate);
  
  const paymentsNeedingRemindersQuery = await db
    .collection('recurringPayments')
    .where('status', '==', 'pending')
    .where('dueDate', '<=', reminderTimestamp)
    .where('reminderSentAt', '==', null)
    .get();

  for (const paymentDoc of paymentsNeedingRemindersQuery.docs) {
    try {
      const paymentData = paymentDoc.data();
      const paymentId = paymentDoc.id;
      
      // Get contract to check reminder settings
      const contractDoc = await db
        .collection('maintenanceContracts')
        .doc(paymentData.maintenanceContractId)
        .get();
      
      if (!contractDoc.exists) {
        continue;
      }
      
      const contract = contractDoc.data();
      const reminderDaysBefore = contract.reminderDaysBefore || 3;
      
      // Check if it's time to send reminder
      const dueDate = paymentData.dueDate.toDate();
      const reminderDate = new Date(dueDate);
      reminderDate.setDate(reminderDate.getDate() - reminderDaysBefore);
      
      if (currentDate.toDate() >= reminderDate) {
        if (!dryRun) {
          // Create reminder notification
          await createPaymentReminderNotification(db, paymentId, paymentData);
          
          // Mark reminder as sent
          await db.collection('recurringPayments').doc(paymentId).update({
            reminderSentAt: FieldValue.serverTimestamp(),
            updatedAt: FieldValue.serverTimestamp(),
          });
        }
        
        logger.info(`Payment reminder sent for: ${paymentId}`);
      }
      
      result.processed++;
      
    } catch (error) {
      const errorMessage = `Error sending reminder for payment ${paymentDoc.id}: ${error}`;
      result.errors.push(errorMessage);
      logger.error(errorMessage);
    }
  }
}

// Helper functions
function calculateNextDueDate(currentDueDate: Timestamp, frequency: string): Timestamp {
  const date = currentDueDate.toDate();
  
  switch (frequency) {
    case 'monthly':
      date.setMonth(date.getMonth() + 1);
      break;
    case 'quarterly':
      date.setMonth(date.getMonth() + 3);
      break;
    case 'yearly':
      date.setFullYear(date.getFullYear() + 1);
      break;
    default:
      throw new Error(`Invalid frequency: ${frequency}`);
  }
  
  return Timestamp.fromDate(date);
}

async function createOverdueNotification(db: any, paymentId: string, paymentData: any) {
  const notificationId = generateNotificationId();
  
  const notification = {
    id: notificationId,
    type: 'payment_overdue',
    title: 'Payment Overdue',
    message: `Recurring payment of ${paymentData.currency} ${paymentData.amount} is now overdue`,
    recipientId: paymentData.clientId,
    recipientEmail: '', // Will be populated by trigger
    relatedEntityId: paymentId,
    relatedEntityType: 'recurring_payment',
    isRead: false,
    sentAt: null,
    createdAt: FieldValue.serverTimestamp(),
  };
  
  await db.collection('paymentNotifications').doc(notificationId).set(notification);
}

async function createPaymentReminderNotification(db: any, paymentId: string, paymentData: any) {
  const notificationId = generateNotificationId();
  
  const notification = {
    id: notificationId,
    type: 'payment_due',
    title: 'Payment Due Soon',
    message: `Recurring payment of ${paymentData.currency} ${paymentData.amount} is due soon`,
    recipientId: paymentData.clientId,
    recipientEmail: '', // Will be populated by trigger
    relatedEntityId: paymentId,
    relatedEntityType: 'recurring_payment',
    isRead: false,
    sentAt: null,
    createdAt: FieldValue.serverTimestamp(),
  };
  
  await db.collection('paymentNotifications').doc(notificationId).set(notification);
}

function generateRecurringPaymentId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `ONE-RPY-${timestamp}-${random}`;
}

function generateNotificationId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `ONE-NOT-${timestamp}-${random}`;
}
