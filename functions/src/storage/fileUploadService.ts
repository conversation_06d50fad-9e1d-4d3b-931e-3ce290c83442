import { onCall } from 'firebase-functions/v2/https';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import { logger } from 'firebase-functions/v2';

interface UploadFileRequest {
  fileName: string;
  fileData: string; // base64 encoded
  mimeType: string;
  fileSize: number;
  entityType: 'recurring_payment' | 'maintenance_contract' | 'project';
  entityId: string;
  description?: string;
}

interface GetFileDownloadUrlRequest {
  storagePath: string;
  expirationHours?: number;
}

interface DeleteFileRequest {
  documentId: string;
  entityType: 'recurring_payment' | 'maintenance_contract' | 'project';
  entityId: string;
}

// Allowed file types and their MIME types
const ALLOWED_FILE_TYPES = {
  // Images
  'image/jpeg': { extension: 'jpg', maxSize: 5 * 1024 * 1024 }, // 5MB
  'image/png': { extension: 'png', maxSize: 5 * 1024 * 1024 },
  'image/gif': { extension: 'gif', maxSize: 5 * 1024 * 1024 },
  'image/webp': { extension: 'webp', maxSize: 5 * 1024 * 1024 },
  
  // Documents
  'application/pdf': { extension: 'pdf', maxSize: 10 * 1024 * 1024 }, // 10MB
  'application/msword': { extension: 'doc', maxSize: 10 * 1024 * 1024 },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { extension: 'docx', maxSize: 10 * 1024 * 1024 },
  'application/vnd.ms-excel': { extension: 'xls', maxSize: 10 * 1024 * 1024 },
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { extension: 'xlsx', maxSize: 10 * 1024 * 1024 },
  'text/plain': { extension: 'txt', maxSize: 1 * 1024 * 1024 }, // 1MB
  'text/csv': { extension: 'csv', maxSize: 5 * 1024 * 1024 },
};

// Upload file to Firebase Storage
export const uploadFile = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as UploadFileRequest;
  const db = getFirestore();
  const storage = getStorage();

  try {
    // Validate file type and size
    const fileTypeInfo = ALLOWED_FILE_TYPES[data.mimeType as keyof typeof ALLOWED_FILE_TYPES];
    if (!fileTypeInfo) {
      throw new Error(`File type not allowed: ${data.mimeType}`);
    }

    if (data.fileSize > fileTypeInfo.maxSize) {
      throw new Error(`File size exceeds limit: ${data.fileSize} bytes (max: ${fileTypeInfo.maxSize} bytes)`);
    }

    // Validate entity exists
    await validateEntity(db, data.entityType, data.entityId);

    // Generate unique document ID and storage path
    const documentId = generateSupportingDocumentId();
    const sanitizedFileName = sanitizeFileName(data.fileName);
    const storagePath = `${data.entityType}/${data.entityId}/documents/${documentId}-${sanitizedFileName}`;
    
    // Convert base64 to buffer
    const fileBuffer = Buffer.from(data.fileData, 'base64');
    
    // Verify actual file size matches reported size
    if (fileBuffer.length !== data.fileSize) {
      throw new Error('File size mismatch');
    }

    // Upload to Firebase Storage
    const bucket = storage.bucket();
    const file = bucket.file(storagePath);
    
    await file.save(fileBuffer, {
      metadata: {
        contentType: data.mimeType,
        metadata: {
          entityType: data.entityType,
          entityId: data.entityId,
          documentId,
          uploadedBy: request.auth.uid,
          originalFileName: data.fileName,
          description: data.description || '',
        }
      }
    });

    // Generate signed URL for download (valid for 1 hour)
    const [downloadUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + 60 * 60 * 1000, // 1 hour
    });

    // Create document record
    const document = {
      id: documentId,
      fileName: `${documentId}-${sanitizedFileName}`,
      originalFileName: data.fileName,
      fileSize: data.fileSize,
      mimeType: data.mimeType,
      storagePath,
      downloadUrl,
      uploadedBy: request.auth.uid,
      uploadedAt: FieldValue.serverTimestamp(),
      description: data.description || null,
    };

    // Update entity with new document
    await addDocumentToEntity(db, data.entityType, data.entityId, document);

    logger.info(`File uploaded successfully: ${documentId} for ${data.entityType}/${data.entityId}`);
    return { success: true, document };

  } catch (error) {
    logger.error('Error uploading file:', error);
    throw error;
  }
});

// Get download URL for a file
export const getFileDownloadUrl = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as GetFileDownloadUrlRequest;
  const storage = getStorage();

  try {
    const bucket = storage.bucket();
    const file = bucket.file(data.storagePath);

    // Check if file exists
    const [exists] = await file.exists();
    if (!exists) {
      throw new Error('File not found');
    }

    // Get file metadata to verify user has access
    const [metadata] = await file.getMetadata();
    const entityType = metadata.metadata?.entityType;
    const entityId = metadata.metadata?.entityId;
    const uploadedBy = metadata.metadata?.uploadedBy;

    // Check access permissions
    const userToken = request.auth.token;
    const userId = request.auth.uid;
    
    if (userToken.role !== 'admin' && uploadedBy !== userId) {
      // For non-admin users, check if they have access to the entity
      const hasAccess = await checkEntityAccess(String(entityType || ''), String(entityId || ''), userId);
      if (!hasAccess) {
        throw new Error('Access denied');
      }
    }

    // Generate signed URL
    const expirationHours = data.expirationHours || 1;
    const [downloadUrl] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + expirationHours * 60 * 60 * 1000,
    });

    return { success: true, downloadUrl };

  } catch (error) {
    logger.error('Error getting download URL:', error);
    throw error;
  }
});

// Delete file
export const deleteFile = onCall(async (request) => {
  if (!request.auth) {
    throw new Error('Must be authenticated');
  }

  const data = request.data as DeleteFileRequest;
  const db = getFirestore();
  const storage = getStorage();

  try {
    // Get entity to find the document
    const entity = await getEntity(db, data.entityType, data.entityId);
    if (!entity) {
      throw new Error('Entity not found');
    }

    const supportingDocuments = entity.supportingDocuments || [];
    const documentIndex = supportingDocuments.findIndex((doc: any) => doc.id === data.documentId);
    
    if (documentIndex === -1) {
      throw new Error('Document not found');
    }

    const document = supportingDocuments[documentIndex];

    // Check permissions
    const userToken = request.auth.token;
    const userId = request.auth.uid;
    
    if (userToken.role !== 'admin' && document.uploadedBy !== userId) {
      throw new Error('Access denied');
    }

    // Delete file from storage
    try {
      const bucket = storage.bucket();
      const file = bucket.file(document.storagePath);
      await file.delete();
    } catch (storageError) {
      logger.warn('Failed to delete file from storage:', storageError);
    }

    // Remove document from entity
    supportingDocuments.splice(documentIndex, 1);
    await updateEntityDocuments(db, data.entityType, data.entityId, supportingDocuments);

    logger.info(`File deleted: ${data.documentId}`);
    return { success: true };

  } catch (error) {
    logger.error('Error deleting file:', error);
    throw error;
  }
});

// Helper functions
async function validateEntity(db: any, entityType: string, entityId: string) {
  let collection: string;
  
  switch (entityType) {
    case 'recurring_payment':
      collection = 'recurringPayments';
      break;
    case 'maintenance_contract':
      collection = 'maintenanceContracts';
      break;
    case 'project':
      collection = 'projects';
      break;
    default:
      throw new Error(`Invalid entity type: ${entityType}`);
  }

  const doc = await db.collection(collection).doc(entityId).get();
  if (!doc.exists) {
    throw new Error(`${entityType} not found: ${entityId}`);
  }
}

async function addDocumentToEntity(db: any, entityType: string, entityId: string, document: any) {
  let collection: string;
  
  switch (entityType) {
    case 'recurring_payment':
      collection = 'recurringPayments';
      break;
    case 'maintenance_contract':
      collection = 'maintenanceContracts';
      break;
    case 'project':
      collection = 'projects';
      break;
    default:
      throw new Error(`Invalid entity type: ${entityType}`);
  }

  const entityRef = db.collection(collection).doc(entityId);
  const entityDoc = await entityRef.get();
  const entityData = entityDoc.data();
  const currentDocs = entityData?.supportingDocuments || [];

  await entityRef.update({
    supportingDocuments: [...currentDocs, document],
    updatedAt: FieldValue.serverTimestamp(),
  });
}

async function getEntity(db: any, entityType: string, entityId: string) {
  let collection: string;
  
  switch (entityType) {
    case 'recurring_payment':
      collection = 'recurringPayments';
      break;
    case 'maintenance_contract':
      collection = 'maintenanceContracts';
      break;
    case 'project':
      collection = 'projects';
      break;
    default:
      throw new Error(`Invalid entity type: ${entityType}`);
  }

  const doc = await db.collection(collection).doc(entityId).get();
  return doc.exists ? doc.data() : null;
}

async function updateEntityDocuments(db: any, entityType: string, entityId: string, documents: any[]) {
  let collection: string;
  
  switch (entityType) {
    case 'recurring_payment':
      collection = 'recurringPayments';
      break;
    case 'maintenance_contract':
      collection = 'maintenanceContracts';
      break;
    case 'project':
      collection = 'projects';
      break;
    default:
      throw new Error(`Invalid entity type: ${entityType}`);
  }

  await db.collection(collection).doc(entityId).update({
    supportingDocuments: documents,
    updatedAt: FieldValue.serverTimestamp(),
  });
}

async function checkEntityAccess(entityType: string, entityId: string, userId: string): Promise<boolean> {
  // This is a simplified access check
  // In a real implementation, you would check if the user has access to the entity
  // based on project membership, client relationships, etc.
  return true; // For now, allow access
}

function sanitizeFileName(fileName: string): string {
  // Remove or replace unsafe characters
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .substring(0, 100); // Limit length
}

function generateSupportingDocumentId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `ONE-DOC-${timestamp}-${random}`;
}
