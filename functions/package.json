{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@sparticuz/chromium": "^121.0.0", "cors": "^2.8.5", "firebase-admin": "^12.6.0", "firebase-functions": "^5.1.1", "puppeteer-core": "^21.11.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/node": "^18.0.0", "typescript": "^5.0.0"}, "private": true}