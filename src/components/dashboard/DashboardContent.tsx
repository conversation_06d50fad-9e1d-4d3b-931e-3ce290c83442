'use client';

import { useAuth } from '@/hooks/useAuth';
import { AuthService } from '@/services/authService';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { User, Shield, LogOut, Settings } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

export function DashboardContent() {
  const { user, signOut, loading } = useAuth();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary/20 border-t-primary"></div>
      </div>
    );
  }

  if (!user) {
    return null; // AuthGuard will handle this
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'destructive';
      case 'support':
        return 'secondary';
      default:
        return 'default';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border/50 bg-background/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <Image
                src="/icon.png"
                alt="Agnex One"
                width={24}
                height={24}
                className="w-6 h-6"
              />
              <span className="text-lg font-semibold">Agnex One</span>
            </Link>
            <Badge variant={getRoleBadgeVariant(user.role)} className="text-xs">
              {user.role}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-3">
            <span className="text-sm text-muted-foreground hidden sm:block">
              {user.displayName || user.email}
            </span>
            <ThemeToggle />
            <Button variant="ghost" size="sm" onClick={handleSignOut} className="text-muted-foreground hover:text-foreground">
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-12">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Welcome back
          </h1>
          <p className="text-muted-foreground">
            Here's what's happening with your account
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Account Information */}
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5 text-muted-foreground" />
                <span>Account Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center py-2 border-b border-border/30 last:border-b-0">
                  <span className="text-sm text-muted-foreground">Email</span>
                  <span className="text-sm font-medium">{user.email}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-border/30 last:border-b-0">
                  <span className="text-sm text-muted-foreground">Display Name</span>
                  <span className="text-sm font-medium">
                    {user.displayName || 'Not set'}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-border/30 last:border-b-0">
                  <span className="text-sm text-muted-foreground">Role</span>
                  <Badge variant={getRoleBadgeVariant(user.role)}>
                    {user.role}
                  </Badge>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm text-muted-foreground">Email Verified</span>
                  <Badge variant={user.emailVerified ? 'default' : 'secondary'}>
                    {user.emailVerified ? 'Verified' : 'Pending'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-muted-foreground" />
                <span>Quick Actions</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start h-10">
                  <User className="h-4 w-4 mr-3" />
                  Update Profile
                </Button>
                
                {AuthService.canAccessAdmin(user) && (
                  <Link href="/admin" className="block">
                    <Button variant="default" className="w-full justify-start h-10">
                      <Shield className="h-4 w-4 mr-3" />
                      Admin Panel
                    </Button>
                  </Link>
                )}
                
                <Button variant="outline" className="w-full justify-start h-10">
                  <Settings className="h-4 w-4 mr-3" />
                  Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Permissions Overview */}
        <div className="mt-8">
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-muted-foreground" />
                <span>Access Permissions</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <span className="text-sm">Admin Access</span>
                  <Badge variant={AuthService.isAdmin(user) ? 'default' : 'secondary'}>
                    {AuthService.isAdmin(user) ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <span className="text-sm">Staff Access</span>
                  <Badge variant={AuthService.canAccessAdmin(user) ? 'default' : 'secondary'}>
                    {AuthService.canAccessAdmin(user) ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <span className="text-sm">Client Access</span>
                  <Badge variant="default">Yes</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Email Verification Notice */}
        {!user.emailVerified && (
          <div className="mt-8">
            <Card className="border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-950/20">
              <CardContent className="pt-6">
                <div className="flex items-start space-x-3">
                  <div className="p-2 rounded-lg bg-yellow-100 dark:bg-yellow-900/50">
                    <Shield className="h-4 w-4 text-yellow-700 dark:text-yellow-300" />
                  </div>
                  <div>
                    <h3 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                      Email Verification Required
                    </h3>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                      Please check your email and verify your account to access all features.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </main>
    </div>
  );
}