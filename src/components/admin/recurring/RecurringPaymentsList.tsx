'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, Card<PERSON>itle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Search, 
  Filter, 
  CheckCircle, 
  Upload,
  Eye,
  Calendar,
  DollarSign,
  FileText,
  MoreHorizontal,
  AlertTriangle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { RecurringPaymentService } from '@/services/recurringPaymentService';
import { 
  RecurringPaymentListItem,
  RecurringPaymentFilt<PERSON>,
  PaymentStatus
} from '@/types/recurring-payments';
import { toast } from 'sonner';

interface RecurringPaymentsListProps {
  onViewPayment: (payment: RecurringPaymentListItem) => void;
  onMarkAsPaid: (payment: RecurringPaymentListItem) => void;
  onUploadDocument: (payment: RecurringPaymentListItem) => void;
}

export function RecurringPaymentsList({
  onViewPayment,
  onMarkAsPaid,
  onUploadDocument
}: RecurringPaymentsListProps) {
  const [payments, setPayments] = useState<RecurringPaymentListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<RecurringPaymentFilters>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  const pageSize = 20;

  useEffect(() => {
    loadPayments(true);
  }, [filters, searchTerm]);

  const loadPayments = async (reset: boolean = false) => {
    setLoading(true);
    try {
      const offset = reset ? 0 : currentPage * pageSize;
      const response = await RecurringPaymentService.getRecurringPayments(
        { ...filters, searchTerm },
        pageSize,
        offset
      );

      if (reset) {
        setPayments(response.payments);
        setCurrentPage(0);
      } else {
        setPayments(prev => [...prev, ...response.payments]);
      }

      setTotal(response.total);
      setHasMore(response.hasMore);
    } catch (error) {
      console.error('Error loading payments:', error);
      toast.error('Failed to load recurring payments');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    setCurrentPage(prev => prev + 1);
    loadPayments(false);
  };

  const handleMarkAsOverdue = async (paymentId: string) => {
    try {
      await RecurringPaymentService.markPaymentAsOverdue(paymentId);
      toast.success('Payment marked as overdue');
      loadPayments(true);
    } catch (error) {
      console.error('Error marking payment as overdue:', error);
      toast.error('Failed to mark payment as overdue');
    }
  };

  const handleSendReminder = async (paymentId: string) => {
    try {
      await RecurringPaymentService.sendPaymentReminder(paymentId);
      toast.success('Payment reminder sent successfully');
    } catch (error) {
      console.error('Error sending reminder:', error);
      toast.error('Failed to send payment reminder');
    }
  };

  const getStatusBadge = (status: PaymentStatus) => {
    const colors = RecurringPaymentService.getPaymentStatusColor(status);
    return (
      <Badge className={colors}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getDaysInfo = (payment: RecurringPaymentListItem) => {
    const today = new Date();
    const dueDate = payment.dueDate.toDate();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (payment.status === 'overdue') {
      const overdueDays = Math.abs(diffDays);
      return {
        text: `${overdueDays} days overdue`,
        color: 'text-red-600'
      };
    } else if (diffDays < 0) {
      return {
        text: `${Math.abs(diffDays)} days past due`,
        color: 'text-red-600'
      };
    } else if (diffDays === 0) {
      return {
        text: 'Due today',
        color: 'text-orange-600'
      };
    } else if (diffDays <= 3) {
      return {
        text: `Due in ${diffDays} days`,
        color: 'text-yellow-600'
      };
    } else {
      return {
        text: `Due in ${diffDays} days`,
        color: 'text-gray-600'
      };
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Recurring Payments</h1>
          <p className="text-muted-foreground">
            Track and manage recurring payment instances
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search payments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={filters.status || 'all'}
              onValueChange={(value) => setFilters({ ...filters, status: value === 'all' ? undefined : value as PaymentStatus })}
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Input
              type="date"
              placeholder="Due date from"
              value={filters.dueDateFrom ? new Date(filters.dueDateFrom).toISOString().split('T')[0] : ''}
              onChange={(e) => setFilters({ 
                ...filters, 
                dueDateFrom: e.target.value ? new Date(e.target.value) : undefined 
              })}
            />

            <Input
              type="date"
              placeholder="Due date to"
              value={filters.dueDateTo ? new Date(filters.dueDateTo).toISOString().split('T')[0] : ''}
              onChange={(e) => setFilters({ 
                ...filters, 
                dueDateTo: e.target.value ? new Date(e.target.value) : undefined 
              })}
            />

            <Button
              variant="outline"
              onClick={() => {
                setFilters({});
                setSearchTerm('');
              }}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Payments Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            Payments ({total})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading && payments.length === 0 ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          ) : payments.length === 0 ? (
            <div className="text-center py-12">
              <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No recurring payments found
              </h3>
              <p className="text-gray-500">
                Payments will appear here as maintenance contracts generate them.
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Contract</TableHead>
                    <TableHead>Project</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Documents</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.map((payment) => {
                    const daysInfo = getDaysInfo(payment);
                    return (
                      <TableRow key={payment.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{payment.contractName}</div>
                            {payment.description && (
                              <div className="text-sm text-muted-foreground">
                                {payment.description}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{payment.projectName}</div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{payment.clientName}</div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">
                            {RecurringPaymentService.formatCurrency(payment.amount, payment.currency)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="text-sm">
                              {RecurringPaymentService.formatDate(payment.dueDate.toDate())}
                            </div>
                            <div className={`text-xs ${daysInfo.color}`}>
                              {daysInfo.text}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(payment.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">
                              {payment.supportingDocumentsCount}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => onViewPayment(payment)}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              
                              {payment.status === 'pending' && (
                                <>
                                  <DropdownMenuItem onClick={() => onMarkAsPaid(payment)}>
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Mark as Paid
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleMarkAsOverdue(payment.id)}>
                                    <AlertTriangle className="h-4 w-4 mr-2" />
                                    Mark as Overdue
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleSendReminder(payment.id)}>
                                    <Calendar className="h-4 w-4 mr-2" />
                                    Send Reminder
                                  </DropdownMenuItem>
                                </>
                              )}
                              
                              <DropdownMenuItem onClick={() => onUploadDocument(payment)}>
                                <Upload className="h-4 w-4 mr-2" />
                                Upload Document
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>

              {hasMore && (
                <div className="flex justify-center mt-6">
                  <Button
                    variant="outline"
                    onClick={handleLoadMore}
                    disabled={loading}
                  >
                    {loading ? 'Loading...' : 'Load More'}
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
