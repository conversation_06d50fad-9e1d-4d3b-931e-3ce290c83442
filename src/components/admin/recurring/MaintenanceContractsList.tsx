'use client';

import { useState, useEffect } from 'react';
import { Card, CardHeader, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  Calendar,
  DollarSign,
  Plus,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { RecurringPaymentService } from '@/services/recurringPaymentService';
import { 
  MaintenanceContractListItem,
  MaintenanceContractFilters,
  RecurringFrequency,
  MaintenanceContractStatus
} from '@/types/recurring-payments';
import { toast } from 'sonner';

interface MaintenanceContractsListProps {
  onCreateContract: () => void;
  onEditContract: (contract: MaintenanceContractListItem) => void;
  onViewContract: (contract: MaintenanceContractListItem) => void;
}

export function MaintenanceContractsList({
  onCreateContract,
  onEditContract,
  onViewContract
}: MaintenanceContractsListProps) {
  const [contracts, setContracts] = useState<MaintenanceContractListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<MaintenanceContractFilters>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  const pageSize = 20;

  useEffect(() => {
    loadContracts(true);
  }, [filters, searchTerm]);

  const loadContracts = async (reset: boolean = false) => {
    setLoading(true);
    try {
      const offset = reset ? 0 : currentPage * pageSize;
      const response = await RecurringPaymentService.getMaintenanceContracts(
        { ...filters, searchTerm },
        pageSize,
        offset
      );

      if (reset) {
        setContracts(response.contracts);
        setCurrentPage(0);
      } else {
        setContracts(prev => [...prev, ...response.contracts]);
      }

      setTotal(response.total);
      setHasMore(response.hasMore);
    } catch (error) {
      console.error('Error loading contracts:', error);
      toast.error('Failed to load maintenance contracts');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    setCurrentPage(prev => prev + 1);
    loadContracts(false);
  };

  const handleDeleteContract = async (contractId: string) => {
    if (!confirm('Are you sure you want to delete this maintenance contract? This will also delete all related recurring payments.')) {
      return;
    }

    try {
      await RecurringPaymentService.deleteMaintenanceContract(contractId);
      toast.success('Maintenance contract deleted successfully');
      loadContracts(true);
    } catch (error) {
      console.error('Error deleting contract:', error);
      toast.error('Failed to delete maintenance contract');
    }
  };

  const getStatusBadge = (status: MaintenanceContractStatus) => {
    const colors = RecurringPaymentService.getContractStatusColor(status);
    return (
      <Badge className={colors}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getFrequencyBadge = (frequency: RecurringFrequency) => {
    const colors = {
      monthly: 'bg-blue-100 text-blue-800',
      quarterly: 'bg-green-100 text-green-800',
      yearly: 'bg-purple-100 text-purple-800'
    };

    return (
      <Badge className={colors[frequency]}>
        {frequency.charAt(0).toUpperCase() + frequency.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Maintenance Contracts</h1>
          <p className="text-muted-foreground">
            Manage recurring maintenance contracts
          </p>
        </div>
        <Button onClick={onCreateContract} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Contract
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search contracts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={filters.status || ''}
              onValueChange={(value) => setFilters({ ...filters, status: value as MaintenanceContractStatus })}
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.frequency || ''}
              onValueChange={(value) => setFilters({ ...filters, frequency: value as RecurringFrequency })}
            >
              <SelectTrigger>
                <SelectValue placeholder="All frequencies" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All frequencies</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => {
                setFilters({});
                setSearchTerm('');
              }}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Contracts Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            Contracts ({total})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading && contracts.length === 0 ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          ) : contracts.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No maintenance contracts found
              </h3>
              <p className="text-gray-500 mb-4">
                Get started by creating your first maintenance contract.
              </p>
              <Button onClick={onCreateContract}>
                Create Contract
              </Button>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Contract</TableHead>
                    <TableHead>Project</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Frequency</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Next Due</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {contracts.map((contract) => (
                    <TableRow key={contract.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{contract.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {contract.totalPaymentsMade} payments made
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{contract.projectName}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{contract.clientName}</div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {RecurringPaymentService.formatCurrency(contract.amount, contract.currency)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Total: {RecurringPaymentService.formatCurrency(contract.totalAmountPaid, contract.currency)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getFrequencyBadge(contract.frequency)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(contract.status)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {RecurringPaymentService.formatDate(contract.nextDueDate.toDate())}
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onViewContract(contract)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onEditContract(contract)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Contract
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDeleteContract(contract.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Contract
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {hasMore && (
                <div className="flex justify-center mt-6">
                  <Button
                    variant="outline"
                    onClick={handleLoadMore}
                    disabled={loading}
                  >
                    {loading ? 'Loading...' : 'Load More'}
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
