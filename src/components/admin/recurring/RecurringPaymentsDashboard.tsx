'use client';

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  DollarSign, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  TrendingUp,
  FileText,
  Plus
} from 'lucide-react';
import { RecurringPaymentService } from '@/services/recurringPaymentService';
import {
  RecurringPaymentSummary,
  UpcomingPayment,
  OverduePayment
} from '@/types/recurring-payments';
import { Timestamp } from 'firebase/firestore';
import { toast } from 'sonner';

interface RecurringPaymentsDashboardProps {
  onCreateContract: () => void;
  onViewContracts: () => void;
  onViewPayments: () => void;
}

export function RecurringPaymentsDashboard({
  onCreateContract,
  onViewContracts,
  onViewPayments
}: RecurringPaymentsDashboardProps) {
  const [summary, setSummary] = useState<RecurringPaymentSummary | null>(null);
  const [upcomingPayments, setUpcomingPayments] = useState<UpcomingPayment[]>([]);
  const [overduePayments, setOverduePayments] = useState<OverduePayment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const [summaryData, upcomingData, overdueData] = await Promise.all([
        RecurringPaymentService.getRecurringPaymentSummary(),
        RecurringPaymentService.getUpcomingPayments(30),
        RecurringPaymentService.getOverduePayments()
      ]);

      setSummary(summaryData);
      setUpcomingPayments(upcomingData);
      setOverduePayments(overdueData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleSendReminder = async (paymentId: string) => {
    try {
      await RecurringPaymentService.sendPaymentReminder(paymentId);
      toast.success('Payment reminder sent successfully');
    } catch (error) {
      console.error('Error sending reminder:', error);
      toast.error('Failed to send payment reminder');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Recurring Payments</h1>
          <p className="text-muted-foreground">
            Manage maintenance contracts and recurring payments
          </p>
        </div>
        <Button onClick={onCreateContract} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Contract
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Active Contracts
                </p>
                <p className="text-2xl font-bold">
                  {summary?.totalActiveContracts || 0}
                </p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Monthly Revenue
                </p>
                <p className="text-2xl font-bold">
                  {RecurringPaymentService.formatCurrency(
                    summary?.totalMonthlyRevenue || 0, 
                    'INR'
                  )}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Upcoming Payments
                </p>
                <p className="text-2xl font-bold">
                  {summary?.upcomingPayments || 0}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Overdue Payments
                </p>
                <p className="text-2xl font-bold text-red-600">
                  {summary?.overduePayments || 0}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onViewContracts}>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <FileText className="h-8 w-8 text-blue-600" />
              <div>
                <h3 className="font-semibold">Manage Contracts</h3>
                <p className="text-sm text-muted-foreground">
                  View and manage maintenance contracts
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onViewPayments}>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div>
                <h3 className="font-semibold">View Payments</h3>
                <p className="text-sm text-muted-foreground">
                  Track recurring payment status
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onCreateContract}>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <Plus className="h-8 w-8 text-purple-600" />
              <div>
                <h3 className="font-semibold">New Contract</h3>
                <p className="text-sm text-muted-foreground">
                  Create a new maintenance contract
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Overdue Payments Alert */}
      {overduePayments.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              Overdue Payments ({overduePayments.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {overduePayments.slice(0, 5).map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-3 bg-white rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{payment.contractName}</span>
                      <Badge variant="destructive">
                        {payment.daysOverdue} days overdue
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {payment.projectName} • {payment.clientName}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">
                      {RecurringPaymentService.formatCurrency(payment.amount, payment.currency)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Due: {RecurringPaymentService.formatDate(payment.dueDate.toDate())}
                    </p>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleSendReminder(payment.id)}
                    className="ml-4"
                  >
                    Send Reminder
                  </Button>
                </div>
              ))}
              {overduePayments.length > 5 && (
                <Button variant="outline" onClick={onViewPayments} className="w-full">
                  View All Overdue Payments ({overduePayments.length - 5} more)
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upcoming Payments */}
      {upcomingPayments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Upcoming Payments (Next 30 Days)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingPayments.slice(0, 5).map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{payment.contractName}</span>
                      <Badge variant={payment.daysUntilDue <= 3 ? "destructive" : "secondary"}>
                        {payment.daysUntilDue} days
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {payment.projectName} • {payment.clientName}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">
                      {RecurringPaymentService.formatCurrency(payment.amount, payment.currency)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Due: {RecurringPaymentService.formatDate(payment.dueDate.toDate())}
                    </p>
                  </div>
                  {payment.daysUntilDue <= 3 && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleSendReminder(payment.id)}
                      className="ml-4"
                    >
                      Send Reminder
                    </Button>
                  )}
                </div>
              ))}
              {upcomingPayments.length > 5 && (
                <Button variant="outline" onClick={onViewPayments} className="w-full">
                  View All Upcoming Payments ({upcomingPayments.length - 5} more)
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* This Month Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>This Month</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Payments Received</span>
                <span className="font-semibold">{summary?.paymentsThisMonth || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Revenue</span>
                <span className="font-semibold">
                  {RecurringPaymentService.formatCurrency(
                    summary?.revenueThisMonth || 0, 
                    'INR'
                  )}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Contracts Expiring</span>
                <span className="font-semibold text-yellow-600">
                  {summary?.contractsExpiringThisMonth || 0}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Stats</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Payment Success Rate</span>
                <span className="font-semibold text-green-600">
                  {summary && summary.paymentsThisMonth > 0 
                    ? Math.round((summary.paymentsThisMonth / (summary.paymentsThisMonth + (summary.overduePayments || 0))) * 100)
                    : 0}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Avg. Contract Value</span>
                <span className="font-semibold">
                  {summary && summary.totalActiveContracts > 0
                    ? RecurringPaymentService.formatCurrency(
                        summary.totalMonthlyRevenue / summary.totalActiveContracts,
                        'INR'
                      )
                    : RecurringPaymentService.formatCurrency(0, 'INR')
                  }
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
