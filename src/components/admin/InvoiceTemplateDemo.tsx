import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { InvoiceTemplatePreview } from './InvoiceTemplatePreview';
import { Download, Eye, FileText, Palette, CheckCircle } from 'lucide-react';

export function InvoiceTemplateDemo() {
  const [selectedTemplate, setSelectedTemplate] = useState<'Professional' | 'Minimal' | 'Modern'>('Professional');

  const templates = [
    {
      id: 'Professional',
      name: 'Professional',
      description: 'Full-featured template with Agnex Studio branding',
      color: '#2563eb',
      isDefault: true,
    },
    {
      id: 'Minimal',
      name: 'Minimal',
      description: 'Clean, simple design for internal use',
      color: '#000000',
      isDefault: false,
    },
    {
      id: 'Modern',
      name: 'Modern',
      description: 'Contemporary design with gradient effects',
      color: '#6366f1',
      isDefault: false,
    },
  ] as const;

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl text-blue-900 flex items-center gap-2">
                <FileText className="h-6 w-6" />
                New Agnex Studio Invoice Templates
              </CardTitle>
              <p className="text-blue-700 mt-2">
                Beautiful, professional, clean, and modern invoice designs ready for use
              </p>
            </div>
            <Badge className="bg-green-100 text-green-800 border-green-200">
              ✅ Transformation Complete
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Template Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Template Variants
          </CardTitle>
          <p className="text-muted-foreground">
            Choose from professionally designed templates that reflect Agnex Studio's brand excellence
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {templates.map((template) => (
              <div
                key={template.id}
                className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  selectedTemplate === template.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedTemplate(template.id as any)}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div
                    className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                    style={{ backgroundColor: template.color }}
                  />
                  <div>
                    <h3 className="font-semibold">{template.name}</h3>
                    {template.isDefault && (
                      <Badge variant="secondary" className="text-xs">
                        Default
                      </Badge>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-600">{template.description}</p>
                
                {selectedTemplate === template.id && (
                  <div className="absolute top-2 right-2">
                    <CheckCircle className="h-5 w-5 text-blue-500" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Template Preview */}
      <Tabs defaultValue="preview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Preview
          </TabsTrigger>
          <TabsTrigger value="features" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Features
          </TabsTrigger>
          <TabsTrigger value="implementation" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Implementation
          </TabsTrigger>
        </TabsList>

        <TabsContent value="preview">
          <InvoiceTemplatePreview 
            templateName={selectedTemplate} 
            showFeatures={false}
          />
        </TabsContent>

        <TabsContent value="features">
          <InvoiceTemplatePreview 
            templateName={selectedTemplate} 
            showFeatures={true}
          />
        </TabsContent>

        <TabsContent value="implementation">
          <Card>
            <CardHeader>
              <CardTitle>🛠 Implementation Details</CardTitle>
              <p className="text-muted-foreground">
                The new invoice template is now active and ready for use
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Current Status */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="border-green-200 bg-green-50">
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-green-800 flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4" />
                      Active Now
                    </h3>
                    <ul className="text-sm text-green-700 space-y-1">
                      <li>✅ Professional template is live</li>
                      <li>✅ Agnex Studio branding integrated</li>
                      <li>✅ All invoices use new design</li>
                      <li>✅ No action required</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card className="border-blue-200 bg-blue-50">
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-blue-800 flex items-center gap-2 mb-2">
                      <Palette className="h-4 w-4" />
                      Enhanced Features
                    </h3>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>🎨 Modern color scheme</li>
                      <li>🏢 Complete branding</li>
                      <li>📄 Professional layout</li>
                      <li>💳 Card-based design</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>

              {/* Code Example */}
              <div>
                <h3 className="font-semibold mb-3">Usage Example</h3>
                <div className="bg-gray-50 p-4 rounded border font-mono text-sm">
                  <div className="text-gray-600">// Invoice generation automatically uses the new template</div>
                  <div className="text-blue-600">const result = await InvoiceService.downloadInvoice(invoiceId);</div>
                  <div className="text-gray-600">// Returns beautiful, professional PDF with Agnex Studio branding</div>
                </div>
              </div>

              {/* File Changes */}
              <div>
                <h3 className="font-semibold mb-3">Files Updated</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                  <div className="bg-gray-50 p-2 rounded border font-mono">
                    📁 functions/src/invoice/downloadInvoice.ts
                  </div>
                  <div className="bg-gray-50 p-2 rounded border font-mono">
                    📁 src/types/invoice.ts
                  </div>
                  <div className="bg-gray-50 p-2 rounded border font-mono">
                    📁 functions/src/invoice/professionalInvoiceTemplate.ts
                  </div>
                  <div className="bg-gray-50 p-2 rounded border font-mono">
                    📁 src/components/admin/InvoiceTemplatePreview.tsx
                  </div>
                </div>
              </div>

              {/* Next Steps */}
              <div className="bg-blue-50 p-4 rounded border border-blue-200">
                <h3 className="font-semibold text-blue-800 mb-2">🚀 Ready for Enhancement</h3>
                <p className="text-blue-700 text-sm mb-3">
                  The template system is now ready for additional customization features:
                </p>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Template selection in admin interface</li>
                  <li>• Logo upload and management</li>
                  <li>• Custom color scheme configuration</li>
                  <li>• Multi-language support</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-lg mb-1">🎉 Transformation Complete!</h3>
              <p className="text-muted-foreground">
                Your invoice template is now beautiful, professional, clean, and modern
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Test Invoice
              </Button>
              <Button className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Generate Sample
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
