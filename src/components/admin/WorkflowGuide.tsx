'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FolderOpen, 
  Ticket, 
  CreditCard, 
  ArrowRight, 
  Plus,
  CheckCircle,
  Users,
  FileText
} from 'lucide-react';
import Link from 'next/link';

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  action?: {
    label: string;
    href: string;
  };
  status?: 'completed' | 'current' | 'upcoming';
}

interface WorkflowGuideProps {
  currentStep?: string;
  showActions?: boolean;
  compact?: boolean;
}

export function WorkflowGuide({ 
  currentStep = 'projects', 
  showActions = true, 
  compact = false 
}: WorkflowGuideProps) {
  const steps: WorkflowStep[] = [
    {
      id: 'projects',
      title: 'Create Project',
      description: 'Start by creating a new project with basic information and budget',
      icon: FolderOpen,
      action: { label: 'Create Project', href: '/admin/projects' },
      status: currentStep === 'projects' ? 'current' : 'completed'
    },
    {
      id: 'clients',
      title: 'Add Clients',
      description: 'Assign clients to the project and set up contact information',
      icon: Users,
      action: { label: 'Manage Clients', href: '/admin/projects' },
      status: currentStep === 'clients' ? 'current' : currentStep === 'projects' ? 'upcoming' : 'completed'
    },
    {
      id: 'tickets',
      title: 'Create Tickets',
      description: 'Break down work into tickets for development, maintenance, or support',
      icon: Ticket,
      action: { label: 'View Tickets', href: '/admin/tickets' },
      status: currentStep === 'tickets' ? 'current' : ['projects', 'clients'].includes(currentStep) ? 'upcoming' : 'completed'
    },
    {
      id: 'payments',
      title: 'Track Payments',
      description: 'Create payment requests and track financial progress',
      icon: CreditCard,
      action: { label: 'View Payments', href: '/admin/payments' },
      status: currentStep === 'payments' ? 'current' : ['projects', 'clients', 'tickets'].includes(currentStep) ? 'upcoming' : 'completed'
    },
    {
      id: 'invoices',
      title: 'Generate Invoices',
      description: 'Create invoices for paid payments and send to clients',
      icon: FileText,
      action: { label: 'View Invoices', href: '/admin/projects' },
      status: currentStep === 'invoices' ? 'current' : 'upcoming'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'current': return 'text-blue-600';
      case 'upcoming': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return CheckCircle;
      case 'current': return ArrowRight;
      case 'upcoming': return Plus;
      default: return Plus;
    }
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2 p-3 bg-muted/20 rounded-lg">
        <span className="text-sm font-medium">Workflow:</span>
        {steps.map((step, index) => {
          const StepIcon = step.icon;
          const StatusIcon = getStatusIcon(step.status || 'upcoming');
          
          return (
            <div key={step.id} className="flex items-center gap-2">
              {index > 0 && <ArrowRight className="h-3 w-3 text-muted-foreground" />}
              <div className={`flex items-center gap-1 ${getStatusColor(step.status || 'upcoming')}`}>
                <StepIcon className="h-4 w-4" />
                <span className="text-sm">{step.title}</span>
                <StatusIcon className="h-3 w-3" />
              </div>
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ArrowRight className="h-5 w-5" />
          Project Management Workflow
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Follow these steps to manage your projects effectively from creation to completion.
          </p>
          
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            {steps.map((step, index) => {
              const StepIcon = step.icon;
              const StatusIcon = getStatusIcon(step.status || 'upcoming');
              
              return (
                <div key={step.id} className="relative">
                  {/* Connector Line */}
                  {index < steps.length - 1 && (
                    <div className="hidden lg:block absolute top-6 left-full w-full h-0.5 bg-muted z-0">
                      <div 
                        className={`h-full transition-all duration-300 ${
                          step.status === 'completed' ? 'bg-green-500 w-full' : 'bg-muted w-0'
                        }`}
                      />
                    </div>
                  )}
                  
                  {/* Step Card */}
                  <Card className={`relative z-10 transition-all duration-300 ${
                    step.status === 'current' ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950' : ''
                  }`}>
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        {/* Step Header */}
                        <div className="flex items-center justify-between">
                          <div className={`p-2 rounded-full ${
                            step.status === 'completed' ? 'bg-green-100 text-green-600 dark:bg-green-900' :
                            step.status === 'current' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900' :
                            'bg-gray-100 text-gray-400 dark:bg-gray-800'
                          }`}>
                            <StepIcon className="h-4 w-4" />
                          </div>
                          <StatusIcon className={`h-4 w-4 ${getStatusColor(step.status || 'upcoming')}`} />
                        </div>
                        
                        {/* Step Content */}
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm">{step.title}</h4>
                          <p className="text-xs text-muted-foreground leading-relaxed">
                            {step.description}
                          </p>
                          
                          {/* Status Badge */}
                          <Badge 
                            variant={
                              step.status === 'completed' ? 'secondary' :
                              step.status === 'current' ? 'default' : 'outline'
                            }
                            className="text-xs"
                          >
                            {step.status === 'completed' ? 'Done' :
                             step.status === 'current' ? 'Active' : 'Pending'}
                          </Badge>
                        </div>
                        
                        {/* Action Button */}
                        {showActions && step.action && (
                          <Button size="sm" variant="outline" className="w-full" asChild>
                            <Link href={step.action.href}>
                              {step.action.label}
                            </Link>
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 