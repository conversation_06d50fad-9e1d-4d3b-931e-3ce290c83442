'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  FolderOpen, 
  Ticket, 
  CreditCard, 
  ArrowRight, 
  ExternalLink,
  User,
  Calendar,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';

interface RelationshipIndicatorProps {
  type: 'project' | 'ticket' | 'payment';
  data: {
    id: string;
    name?: string;
    title?: string;
    amount?: number;
    currency?: string;
    status: string;
    createdAt?: any;
    projectId?: string;
    projectName?: string;
    ticketId?: string;
    ticketTitle?: string;
    paymentId?: string;
    clientName?: string;
    assignedToName?: string;
  };
  showRelated?: boolean;
  compact?: boolean;
}

export function RelationshipIndicator({ 
  type, 
  data, 
  showRelated = true, 
  compact = false 
}: RelationshipIndicatorProps) {
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    try {
      let date: Date;
      if (timestamp instanceof Date) {
        date = timestamp;
      } else if (timestamp && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp && timestamp.seconds) {
        date = new Date(timestamp.seconds * 1000);
      } else {
        return 'N/A';
      }
      return format(date, 'MMM dd, yyyy');
    } catch {
      return 'N/A';
    }
  };

  const formatCurrency = (amount: number, currency = 'INR') => {
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount);
  };

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'paid':
      case 'completed':
        return 'secondary';
      case 'pending':
      case 'open':
      case 'in_progress':
        return 'default';
      case 'draft':
        return 'outline';
      case 'overdue':
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'project': return FolderOpen;
      case 'ticket': return Ticket;
      case 'payment': return CreditCard;
      default: return FolderOpen;
    }
  };

  const getMainHref = () => {
    switch (type) {
      case 'project': return `/admin/projects/${data.id}`;
      case 'ticket': return `/admin/projects/${data.projectId}#tickets`;
      case 'payment': return `/admin/projects/${data.projectId}#payments`;
      default: return '#';
    }
  };

  const getMainLabel = () => {
    switch (type) {
      case 'project': return data.name || 'Unknown Project';
      case 'ticket': return data.title || 'Unknown Ticket';
      case 'payment': return data.amount ? formatCurrency(data.amount, data.currency) : 'Unknown Amount';
      default: return 'Unknown';
    }
  };

  const Icon = getIcon();

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <Icon className="h-4 w-4 text-muted-foreground" />
        <Link href={getMainHref()} className="text-sm text-blue-600 hover:text-blue-800">
          {getMainLabel()}
        </Link>
        <Badge variant={getStatusBadgeVariant(data.status)} className="text-xs">
          {data.status}
        </Badge>
      </div>
    );
  }

  return (
    <Card className="p-3">
      <CardContent className="p-0">
        <div className="space-y-3">
          {/* Main Item */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Icon className="h-5 w-5 text-primary" />
              <div>
                <Link href={getMainHref()} className="font-medium text-blue-600 hover:text-blue-800">
                  {getMainLabel()}
                </Link>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant={getStatusBadgeVariant(data.status)} className="text-xs">
                    {data.status}
                  </Badge>
                  {data.createdAt && (
                    <span className="text-xs text-muted-foreground flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(data.createdAt)}
                    </span>
                  )}
                </div>
              </div>
            </div>
            <Button size="sm" variant="outline" asChild>
              <Link href={getMainHref()}>
                <ExternalLink className="h-3 w-3 mr-1" />
                View
              </Link>
            </Button>
          </div>

          {/* Related Items */}
          {showRelated && (
            <div className="space-y-2 pl-8 border-l-2 border-muted">
              {/* Project Connection */}
              {type !== 'project' && data.projectId && (
                <div className="flex items-center gap-2">
                  <FolderOpen className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">Project:</span>
                  <Link 
                    href={`/admin/projects/${data.projectId}`}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    {data.projectName || 'Unknown Project'}
                  </Link>
                </div>
              )}

              {/* Ticket Connection */}
              {type === 'payment' && data.ticketId && (
                <div className="flex items-center gap-2">
                  <Ticket className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">Ticket:</span>
                  <Link 
                    href={`/admin/projects/${data.projectId}#tickets`}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    {data.ticketTitle || 'Unknown Ticket'}
                  </Link>
                </div>
              )}

              {/* Client/User Info */}
              {(data.clientName || data.assignedToName) && (
                <div className="flex items-center gap-2">
                  <User className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    {data.clientName ? 'Client:' : 'Assigned:'}
                  </span>
                  <span className="text-xs text-foreground">
                    {data.clientName || data.assignedToName}
                  </span>
                </div>
              )}

              {/* Amount for non-payment types */}
              {type !== 'payment' && data.amount && (
                <div className="flex items-center gap-2">
                  <DollarSign className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">Amount:</span>
                  <span className="text-xs text-foreground">
                    {formatCurrency(data.amount, data.currency)}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 