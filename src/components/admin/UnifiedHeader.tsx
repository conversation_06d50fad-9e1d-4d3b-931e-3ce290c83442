'use client';

import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Home, 
  ChevronRight, 
  FolderOpen, 
  Ticket, 
  CreditCard, 
  Plus,
  ArrowLeft,
  Search,
  Filter,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';

interface UnifiedHeaderProps {
  title: string;
  subtitle?: string;
  currentProject?: {
    id: string;
    name: string;
    status: string;
  };
  currentTicket?: {
    id: string;
    title: string;
    status: string;
  };
  currentPayment?: {
    id: string;
    amount: number;
    currency: string;
    status: string;
  };
  actions?: React.ReactNode;
  breadcrumbs?: Array<{
    label: string;
    href?: string;
    icon?: React.ComponentType<{ className?: string }>;
  }>;
  stats?: Array<{
    label: string;
    value: string | number;
    color?: string;
    icon?: React.ComponentType<{ className?: string }>;
  }>;
}

export function UnifiedHeader({ 
  title, 
  subtitle, 
  currentProject,
  currentTicket,
  currentPayment,
  actions,
  breadcrumbs,
  stats 
}: UnifiedHeaderProps) {
  const pathname = usePathname();

  // Auto-generate breadcrumbs based on pathname if not provided
  const generateBreadcrumbs = () => {
    if (breadcrumbs) return breadcrumbs;
    
    const crumbs = [{ label: 'Admin', href: '/admin', icon: Home }];
    
    if (pathname.includes('/projects')) {
      crumbs.push({ label: 'Projects', href: '/admin/projects', icon: FolderOpen });
      if (currentProject) {
        crumbs.push({ label: currentProject.name, href: `/admin/projects/${currentProject.id}`, icon: FolderOpen });
      }
    } else if (pathname.includes('/tickets')) {
      crumbs.push({ label: 'Tickets', href: '/admin/tickets', icon: Ticket });
      if (currentTicket) {
        crumbs.push({ label: currentTicket.title, href: '#', icon: Ticket });
      }
    } else if (pathname.includes('/payments')) {
      crumbs.push({ label: 'Payments', href: '/admin/payments', icon: CreditCard });
      if (currentPayment) {
        crumbs.push({ label: `Payment ${currentPayment.id.slice(-6)}`, href: '#', icon: CreditCard });
      }
    }
    
    return crumbs;
  };

  const finalBreadcrumbs = generateBreadcrumbs();

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'paid':
      case 'completed':
        return 'secondary';
      case 'pending':
      case 'open':
      case 'in_progress':
        return 'default';
      case 'draft':
        return 'outline';
      case 'overdue':
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  return (
    <div className="space-y-4">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center text-sm text-muted-foreground">
        {finalBreadcrumbs.map((crumb, index) => (
          <div key={index} className="flex items-center">
            {index > 0 && <ChevronRight className="h-3 w-3 mx-2" />}
            {crumb.href ? (
              <Link href={crumb.href} className="flex items-center gap-1 hover:text-foreground transition-colors">
                {crumb.icon && <crumb.icon className="h-3 w-3" />}
                {crumb.label}
              </Link>
            ) : (
              <span className="flex items-center gap-1 text-foreground">
                {crumb.icon && <crumb.icon className="h-3 w-3" />}
                {crumb.label}
              </span>
            )}
          </div>
        ))}
      </div>

      {/* Main Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">{title}</h1>
            {subtitle && (
              <p className="text-muted-foreground mt-1">{subtitle}</p>
            )}
          </div>
          
          {/* Context Indicators */}
          <div className="flex items-center gap-2">
            {currentProject && (
              <div className="flex items-center gap-2">
                <Badge variant={getStatusBadgeVariant(currentProject.status)}>
                  {currentProject.status}
                </Badge>
                <span className="text-sm text-muted-foreground">Project</span>
              </div>
            )}
            {currentTicket && (
              <div className="flex items-center gap-2">
                <Badge variant={getStatusBadgeVariant(currentTicket.status)}>
                  {currentTicket.status}
                </Badge>
                <span className="text-sm text-muted-foreground">Ticket</span>
              </div>
            )}
            {currentPayment && (
              <div className="flex items-center gap-2">
                <Badge variant={getStatusBadgeVariant(currentPayment.status)}>
                  {currentPayment.status}
                </Badge>
                <span className="text-sm text-muted-foreground">Payment</span>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>

      {/* Stats Row */}
      {stats && stats.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {stats.map((stat, index) => (
            <Card key={index} className="p-3">
              <CardContent className="p-0">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">{stat.label}</p>
                    <p className={`text-lg font-bold ${stat.color || 'text-foreground'}`}>
                      {stat.value}
                    </p>
                  </div>
                  {stat.icon && (
                    <stat.icon className="h-4 w-4 text-muted-foreground" />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Quick Actions Bar */}
      <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Quick Actions:</span>
          <Button size="sm" variant="outline" asChild>
            <Link href="/admin/projects">
              <FolderOpen className="h-3 w-3 mr-1" />
              Projects
            </Link>
          </Button>
          <Button size="sm" variant="outline" asChild>
            <Link href="/admin/tickets">
              <Ticket className="h-3 w-3 mr-1" />
              Tickets
            </Link>
          </Button>
          <Button size="sm" variant="outline" asChild>
            <Link href="/admin/payments">
              <CreditCard className="h-3 w-3 mr-1" />
              Payments
            </Link>
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-xs text-muted-foreground">
            {pathname.includes('/projects') && 'Manage projects and their lifecycle'}
            {pathname.includes('/tickets') && 'Track development and support tasks'}
            {pathname.includes('/payments') && 'Monitor financial transactions'}
          </span>
        </div>
      </div>
    </div>
  );
} 