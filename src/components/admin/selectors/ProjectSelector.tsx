'use client';

import { useState, useEffect } from 'react';
import { useProjectManagement } from '@/hooks/useProjectManagement';
import { ProjectStatus } from '@/types/project';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, FolderOpen } from 'lucide-react';

interface ProjectSelectorProps {
  value: string;
  onChange: (projectId: string) => void;
  statusFilter?: ProjectStatus[];
  placeholder?: string;
  disabled?: boolean;
  error?: string;
}

export function ProjectSelector({
  value,
  onChange,
  statusFilter,
  placeholder = "Select a project",
  disabled = false,
  error
}: ProjectSelectorProps) {
  const { projects, loadProjects, projectFilters, setProjectFilters } = useProjectManagement();
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  // Filter projects based on status and search
  const filteredProjects = projects.filter(project => {
    const matchesStatus = !statusFilter || statusFilter.includes(project.status);
    const matchesSearch = searchTerm === '' || 
      project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesSearch;
  });

  const selectedProject = projects.find(project => project.id === value);

  useEffect(() => {
    // Load projects on component mount
    loadProjects(true);
  }, []);

  useEffect(() => {
    // Update search filter when searchTerm changes
    if (searchTerm !== (projectFilters.searchTerm || '')) {
      setProjectFilters({ ...projectFilters, searchTerm });
    }
  }, [searchTerm]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleProjectSelect = (projectId: string) => {
    onChange(projectId);
    setIsOpen(false);
    setSearchTerm('');
  };

  const getStatusBadgeVariant = (status: ProjectStatus): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'draft':
        return 'outline';
      case 'active':
        return 'default';
      case 'completed':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  if (filteredProjects.length === 0 && !searchTerm) {
    return (
      <div className="flex items-center gap-2 p-3 text-sm text-muted-foreground border rounded-md">
        <FolderOpen className="h-4 w-4" />
        {statusFilter ? 
          `No ${statusFilter.join(', ')} projects available` : 
          'No projects available'
        }
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Select 
        open={isOpen} 
        onOpenChange={setIsOpen}
        value={value} 
        onValueChange={handleProjectSelect}
        disabled={disabled}
      >
        <SelectTrigger className={error ? "border-red-500" : ""}>
          <SelectValue placeholder={placeholder}>
            {selectedProject ? (
              <div className="flex items-center gap-2">
                <div className="flex flex-col text-left flex-1">
                  <span className="font-medium">{selectedProject.name}</span>
                  <span className="text-xs text-muted-foreground truncate">
                    {selectedProject.description}
                  </span>
                </div>
                <Badge variant={getStatusBadgeVariant(selectedProject.status)} className="ml-auto">
                  {selectedProject.status.replace('_', ' ')}
                </Badge>
              </div>
            ) : placeholder}
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent>
          <div className="p-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search projects..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="pl-8"
                autoFocus
              />
            </div>
          </div>
          
          <div className="max-h-60 overflow-y-auto">
            {filteredProjects.length === 0 ? (
              <div className="p-2 text-sm text-muted-foreground text-center">
                No projects found
              </div>
            ) : (
              filteredProjects.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  <div className="flex items-center gap-2 w-full">
                    <div className="flex flex-col flex-1">
                      <span className="font-medium">{project.name}</span>
                      <span className="text-xs text-muted-foreground truncate">
                        {project.description}
                      </span>
                    </div>
                    <Badge variant={getStatusBadgeVariant(project.status)} className="ml-auto">
                      {project.status.replace('_', ' ')}
                    </Badge>
                  </div>
                </SelectItem>
              ))
            )}
          </div>
        </SelectContent>
      </Select>
      
      {error && (
        <p className="text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
    </div>
  );
} 