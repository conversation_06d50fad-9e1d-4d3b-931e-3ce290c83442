'use client';

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  DollarSign, 
  AlertTriangle, 
  TrendingUp,
  Clock,
  FileText,
  ArrowRight
} from 'lucide-react';
import { RecurringPaymentService } from '@/services/recurringPaymentService';
import { 
  RecurringPaymentSummary, 
  UpcomingPayment, 
  OverduePayment 
} from '@/types/recurring-payments';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

export function RecurringPaymentsWidget() {
  const router = useRouter();
  const [summary, setSummary] = useState<RecurringPaymentSummary | null>(null);
  const [upcomingPayments, setUpcomingPayments] = useState<UpcomingPayment[]>([]);
  const [overduePayments, setOverduePayments] = useState<OverduePayment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [summaryData, upcomingData, overdueData] = await Promise.all([
        RecurringPaymentService.getRecurringPaymentSummary(),
        RecurringPaymentService.getUpcomingPayments(7), // Next 7 days
        RecurringPaymentService.getOverduePayments()
      ]);

      setSummary(summaryData);
      setUpcomingPayments(upcomingData);
      setOverduePayments(overdueData);
    } catch (error) {
      console.error('Error loading recurring payments data:', error);
      toast.error('Failed to load recurring payments data');
    } finally {
      setLoading(false);
    }
  };

  const handleViewAll = () => {
    router.push('/admin/recurring-payments');
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recurring Payments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recurring Payments
          </div>
          <Button variant="ghost" size="sm" onClick={handleViewAll}>
            <ArrowRight className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-blue-600" />
              <span className="text-sm text-muted-foreground">Active Contracts</span>
            </div>
            <p className="text-2xl font-bold">{summary?.totalActiveContracts || 0}</p>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm text-muted-foreground">Monthly Revenue</span>
            </div>
            <p className="text-lg font-semibold">
              {RecurringPaymentService.formatCurrency(
                summary?.totalMonthlyRevenue || 0, 
                'INR'
              )}
            </p>
          </div>
        </div>

        {/* Alert Section */}
        {(overduePayments.length > 0 || upcomingPayments.length > 0) && (
          <div className="space-y-3">
            {/* Overdue Payments Alert */}
            {overduePayments.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800">
                    {overduePayments.length} Overdue Payment{overduePayments.length > 1 ? 's' : ''}
                  </span>
                </div>
                <div className="space-y-2">
                  {overduePayments.slice(0, 2).map((payment) => (
                    <div key={payment.id} className="flex justify-between items-center text-sm">
                      <div>
                        <span className="font-medium">{payment.contractName}</span>
                        <span className="text-red-600 ml-2">
                          ({payment.daysOverdue} days overdue)
                        </span>
                      </div>
                      <span className="font-semibold text-red-700">
                        {RecurringPaymentService.formatCurrency(payment.amount, payment.currency)}
                      </span>
                    </div>
                  ))}
                  {overduePayments.length > 2 && (
                    <p className="text-xs text-red-600">
                      +{overduePayments.length - 2} more overdue payments
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Upcoming Payments */}
            {upcomingPayments.length > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">
                    {upcomingPayments.length} Payment{upcomingPayments.length > 1 ? 's' : ''} Due This Week
                  </span>
                </div>
                <div className="space-y-2">
                  {upcomingPayments.slice(0, 2).map((payment) => (
                    <div key={payment.id} className="flex justify-between items-center text-sm">
                      <div>
                        <span className="font-medium">{payment.contractName}</span>
                        <span className="text-yellow-600 ml-2">
                          (in {payment.daysUntilDue} day{payment.daysUntilDue > 1 ? 's' : ''})
                        </span>
                      </div>
                      <span className="font-semibold text-yellow-700">
                        {RecurringPaymentService.formatCurrency(payment.amount, payment.currency)}
                      </span>
                    </div>
                  ))}
                  {upcomingPayments.length > 2 && (
                    <p className="text-xs text-yellow-600">
                      +{upcomingPayments.length - 2} more upcoming payments
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4 pt-3 border-t">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">This Month</p>
            <div className="space-y-1">
              <p className="text-lg font-semibold">
                {summary?.paymentsThisMonth || 0}
              </p>
              <p className="text-xs text-muted-foreground">Payments</p>
            </div>
          </div>
          
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Revenue</p>
            <div className="space-y-1">
              <p className="text-lg font-semibold">
                {RecurringPaymentService.formatCurrency(
                  summary?.revenueThisMonth || 0, 
                  'INR'
                )}
              </p>
              <p className="text-xs text-muted-foreground">This Month</p>
            </div>
          </div>
        </div>

        {/* No Data State */}
        {!summary?.totalActiveContracts && (
          <div className="text-center py-6">
            <Calendar className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-muted-foreground mb-3">
              No recurring payments set up yet
            </p>
            <Button size="sm" onClick={handleViewAll}>
              Set Up Recurring Payments
            </Button>
          </div>
        )}

        {/* Action Button */}
        {summary?.totalActiveContracts && (
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={handleViewAll}
          >
            View All Recurring Payments
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
