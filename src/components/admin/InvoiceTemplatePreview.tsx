import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileText, Download, Eye, Palette } from 'lucide-react';

interface InvoiceTemplatePreviewProps {
  templateName?: 'Professional' | 'Minimal' | 'Modern';
  showFeatures?: boolean;
}

export function InvoiceTemplatePreview({ 
  templateName = 'Professional',
  showFeatures = true 
}: InvoiceTemplatePreviewProps) {
  const getTemplateColors = (template: string) => {
    switch (template) {
      case 'Professional':
        return {
          primary: '#2563eb',
          secondary: '#f8fafc',
          accent: '#10b981',
        };
      case 'Minimal':
        return {
          primary: '#000000',
          secondary: '#f5f5f5',
          accent: '#333333',
        };
      case 'Modern':
        return {
          primary: '#6366f1',
          secondary: '#f1f5f9',
          accent: '#06d6a0',
        };
      default:
        return {
          primary: '#2563eb',
          secondary: '#f8fafc',
          accent: '#10b981',
        };
    }
  };

  const colors = getTemplateColors(templateName);

  return (
    <div className="space-y-6">
      {/* Template Preview Card */}
      <Card className="border-2 border-gray-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              {templateName} Invoice Template Preview
            </CardTitle>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              New Design
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {/* Mock Invoice Preview */}
          <div className="bg-white border rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
            {/* Header */}
            <div 
              className="rounded-t-lg p-4 text-white relative"
              style={{ backgroundColor: colors.primary }}
            >
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-white bg-opacity-20 rounded border border-white border-opacity-30 flex items-center justify-center">
                    <FileText className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-xl font-bold">AGNEX STUDIO</h1>
                    <p className="text-sm opacity-90">agnex.in | <EMAIL></p>
                    <p className="text-xs opacity-75">GSTIN: 32COLPG6895J1ZE</p>
                  </div>
                </div>
                <div className="text-right">
                  <h2 className="text-2xl font-bold">INVOICE</h2>
                  <p className="text-sm">#INV-2025-001</p>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-4 space-y-4">
              {/* Details Cards */}
              <div className="grid grid-cols-2 gap-4">
                <div 
                  className="p-3 rounded border"
                  style={{ backgroundColor: colors.secondary }}
                >
                  <h3 className="font-semibold text-sm mb-2">INVOICE DETAILS</h3>
                  <div className="text-xs space-y-1">
                    <p>Issue Date: Dec 30, 2025</p>
                    <p>Due Date: Jan 30, 2026</p>
                    <p>Status: SENT</p>
                  </div>
                </div>
                <div 
                  className="p-3 rounded border"
                  style={{ backgroundColor: colors.secondary }}
                >
                  <h3 className="font-semibold text-sm mb-2">PROJECT DETAILS</h3>
                  <div className="text-xs space-y-1">
                    <p>Project: E-commerce Website</p>
                    <p>Type: Individual Payment</p>
                    <p>Currency: INR</p>
                  </div>
                </div>
              </div>

              {/* Client Info */}
              <div className="border rounded p-3">
                <h3 className="font-semibold text-sm mb-2" style={{ color: colors.primary }}>
                  BILL TO
                </h3>
                <div className="text-sm">
                  <p className="font-semibold">Client Name</p>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>

              {/* Table */}
              <div className="border rounded overflow-hidden">
                <div 
                  className="p-2 text-white text-sm font-semibold"
                  style={{ backgroundColor: colors.primary }}
                >
                  <div className="grid grid-cols-3 gap-4">
                    <span>Description</span>
                    <span>Date</span>
                    <span className="text-right">Amount</span>
                  </div>
                </div>
                <div className="p-2 text-sm">
                  <div className="grid grid-cols-3 gap-4">
                    <span>Payment for E-commerce Website</span>
                    <span>Dec 30, 2025</span>
                    <span className="text-right font-semibold">₹50,000</span>
                  </div>
                </div>
              </div>

              {/* Totals */}
              <div className="flex justify-end">
                <div className="w-64 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal:</span>
                    <span>₹50,000</span>
                  </div>
                  <div 
                    className="flex justify-between text-white font-bold p-2 rounded"
                    style={{ backgroundColor: colors.accent }}
                  >
                    <span>TOTAL:</span>
                    <span>₹50,000</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div 
              className="rounded-b-lg p-3 text-xs"
              style={{ backgroundColor: colors.secondary }}
            >
              <p className="text-center text-gray-600">
                Thank you for choosing Agnex Studio for your project needs.
              </p>
              <p className="text-center text-gray-500 mt-1">
                Generated on Dec 30, 2025 | AGNEX STUDIO - Digital Excellence
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features Highlight */}
      {showFeatures && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">✨ Template Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <h4 className="font-semibold text-sm">🎨 Design Excellence</h4>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Professional color scheme</li>
                  <li>• Modern card-based layout</li>
                  <li>• Clean typography hierarchy</li>
                  <li>• Responsive visual elements</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-sm">🏢 Brand Integration</h4>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Agnex Studio branding</li>
                  <li>• Company contact details</li>
                  <li>• GSTIN compliance</li>
                  <li>• Professional footer</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-sm">📄 Professional Structure</h4>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Organized information sections</li>
                  <li>• Clear payment breakdown</li>
                  <li>• Terms and conditions</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-sm">⚡ Technical Features</h4>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• High-quality PDF generation</li>
                  <li>• Multi-template support</li>
                  <li>• Mobile-friendly layout</li>
                  <li>• Fast processing</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Before/After Comparison */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">📈 Transformation Impact</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold text-red-700">❌ Previous Template</h4>
              <div className="bg-gray-50 p-4 rounded border text-sm">
                <ul className="space-y-2 text-gray-600">
                  <li>• Basic text-only layout</li>
                  <li>• No branding or colors</li>
                  <li>• Simple table structure</li>
                  <li>• Generic appearance</li>
                  <li>• Limited visual appeal</li>
                </ul>
              </div>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold text-green-700">✅ New Professional Template</h4>
              <div className="bg-green-50 p-4 rounded border text-sm">
                <ul className="space-y-2 text-gray-600">
                  <li>• Modern card-based design</li>
                  <li>• Full Agnex Studio branding</li>
                  <li>• Professional color scheme</li>
                  <li>• Enhanced visual hierarchy</li>
                  <li>• Premium client experience</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
