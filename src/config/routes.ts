export const ROUTES = {
  HOME: '/',
  
  // Auth routes
  AUTH: {
    LOGIN: '/auth/login',
  },
  
  // Dashboard routes
  DASHBOARD: '/dashboard',
  
  // Admin routes
  ADMIN: {
    ROOT: '/admin',
    USERS: '/admin/users',
    PROJECTS: '/admin/projects',
    RECURRING_PAYMENTS: '/admin/recurring-payments',
    ACTIVITY: '/admin/activity',
    SETTINGS: '/admin/settings',
  },
  
  // Client routes (future)
  CLIENT: {
    ROOT: '/client',
    PROJECTS: '/client/projects',
  },
  
  // Support routes (future)
  SUPPORT: {
    ROOT: '/support',
  },
} as const;

export const PUBLIC_ROUTES = [
  ROUTES.HOME,
  ROUTES.AUTH.LOGIN,
] as const;

export const PROTECTED_ROUTES = [
  ROUTES.DASHBOARD,
  ...Object.values(ROUTES.ADMIN),
  ...Object.values(ROUTES.CLIENT),
  ...Object.values(ROUTES.SUPPORT),
] as const;

export const ADMIN_ROUTES = Object.values(ROUTES.ADMIN); 