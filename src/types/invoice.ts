import { Timestamp } from 'firebase/firestore';

// Invoice Types
export type InvoiceType = 'individual' | 'consolidated';
export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';

// Individual Payment Invoice
export interface Invoice {
  id: string; // Invoice number (e.g., INV-2025-001)
  invoiceNumber: string; // Human-readable invoice number
  type: InvoiceType;
  projectId: string;
  clientId: string;
  
  // Financial details
  amount: number;
  currency: string;
  totalAmount: number; // same as amount (no tax)
  
  // Invoice metadata
  status: InvoiceStatus;
  issueDate: Timestamp;
  dueDate: Timestamp;
  paidDate?: Timestamp;
  
  // Content
  description: string;
  notes?: string;
  terms?: string;
  
  // Related entities
  paymentIds: string[]; // For individual: single payment, for consolidated: multiple
  linkedTicketIds?: string[]; // Optional reference to tickets
  
  // Generation metadata
  generatedBy: string; // User ID who generated the invoice
  generatedAt: Timestamp;
  pdfUrl?: string; // URL to generated PDF

  // Regeneration tracking
  lastRegeneratedBy?: string;
  lastRegeneratedAt?: Timestamp;
  regenerationCount?: number;
  lastAutoRegeneratedAt?: Timestamp;
  autoRegenerationCount?: number;

  // Tracking
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Company/Business Configuration for Invoice
export interface CompanyConfig {
  id: string;
  name: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  contact: {
    email: string;
    phone: string;
    website?: string;
  };
  business: {
    taxId?: string; // GST/VAT number
    registrationNumber?: string;
    panNumber?: string; // For India
  };
  banking?: {
    accountName: string;
    accountNumber: string;
    bankName: string;
    ifscCode?: string; // For India
    swiftCode?: string; // For international
  };
  branding?: {
    logoUrl?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
  invoiceSettings: {
    defaultTerms: string;
    defaultDueDays: number; // Default days until due
    invoicePrefix: string; // e.g., "INV"
    startingNumber: number;
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Invoice Template Configuration
export interface InvoiceTemplate {
  id: string;
  name: string;
  type: 'standard' | 'minimal' | 'detailed';
  template: {
    headerSection: {
      showLogo: boolean;
      showCompanyAddress: boolean;
      showContact: boolean;
    };
    clientSection: {
      showBillingAddress: boolean;
      showClientDetails: boolean;
    };
    itemsSection: {
      showDescription: boolean;
      showQuantity: boolean;
      showRate: boolean;
      showAmount: boolean;
    };
    footerSection: {
      showTerms: boolean;
      showNotes: boolean;
      showBankDetails: boolean;
    };
  };
  styling: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    fontSize: number;
  };
  isDefault: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Professional HTML Invoice Template Configuration
export interface ProfessionalInvoiceTemplate {
  id: string;
  name: string;
  type: 'professional' | 'modern' | 'minimal' | 'classic';
  template: {
    layout: {
      format: 'A4' | 'Letter';
      orientation: 'portrait' | 'landscape';
      margins: {
        top: string;
        right: string;
        bottom: string;
        left: string;
      };
      showWatermark: boolean;
    };
    headerSection: {
      showLogo: boolean;
      logoPosition: 'left' | 'center' | 'right';
      showCompanyAddress: boolean;
      showContact: boolean;
      showBranding: boolean;
      backgroundStyle: 'solid' | 'gradient' | 'pattern';
      height: string;
    };
    clientSection: {
      showBillingAddress: boolean;
      showClientDetails: boolean;
      sectionStyle: 'card' | 'bordered' | 'minimal';
    };
    itemsSection: {
      tableStyle: 'modern' | 'classic' | 'minimal';
      showDescription: boolean;
      showQuantity: boolean;
      showRate: boolean;
      showAmount: boolean;
      showPaymentType: boolean;
      alternateRowColors: boolean;
    };
    totalsSection: {
      style: 'card' | 'inline' | 'highlighted';
      showSubtotal: boolean;
      highlightTotal: boolean;
      position: 'right' | 'center' | 'left';
    };
    footerSection: {
      showTerms: boolean;
      showNotes: boolean;
      showBankDetails: boolean;
      showThankYouMessage: boolean;
      showGenerationInfo: boolean;
      height: string;
    };
  };
  styling: {
    colorScheme: {
      primary: string;
      secondary: string;
      accent: string;
      text: string;
      muted: string;
      background: string;
      success: string;
      warning: string;
      error: string;
    };
    typography: {
      fontFamily: 'Inter' | 'Helvetica' | 'Times' | 'Courier';
      headerSize: string;
      bodySize: string;
      footerSize: string;
      lineHeight: number;
    };
    spacing: {
      sectionGap: string;
      cardPadding: string;
      tableRowHeight: string;
    };
  };
  branding: {
    companyName: string;
    tagline?: string;
    logoUrl?: string;
    website?: string;
    address?: string;
    phone?: string;
    email?: string;
    showGSTIN: boolean;
    gstin?: string;
    customFooterText?: string;
  };
  isDefault: boolean;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Invoice Generation Options
export interface InvoiceGenerationOptions {
  templateId?: string;
  customStyling?: Partial<ProfessionalInvoiceTemplate['styling']>;
  includeNotes?: boolean;
  includeTerms?: boolean;
  watermarkText?: string;
  language?: 'en' | 'hi';
  dateFormat?: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
  currencyFormat?: 'symbol' | 'code' | 'both';
  htmlTemplate?: boolean; // New option for HTML-based templates
}

// Input types for forms
export interface InvoiceInput {
  type: InvoiceType;
  projectId: string;
  clientId: string;
  paymentIds: string[];
  description: string;
  notes?: string;
  dueDate: Date;
}

export interface ConsolidatedInvoiceInput {
  projectId: string;
  clientId: string;
  paymentIds: string[];
  dateRange: {
    from: Date;
    to: Date;
  };
  description: string;
  notes?: string;
  dueDate: Date;
  groupBy?: 'month' | 'paymentType' | 'none';
}

// Response types
export interface InvoiceListItem extends Invoice {
  projectName?: string;
  clientName?: string;
  generatedByName?: string;
}

export interface InvoiceListResponse {
  invoices: InvoiceListItem[];
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

// Filter types
export interface InvoiceFilters {
  projectId?: string;
  clientId?: string;
  status?: InvoiceStatus;
  type?: InvoiceType;
  dateRange?: {
    from: Date;
    to: Date;
  };
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
