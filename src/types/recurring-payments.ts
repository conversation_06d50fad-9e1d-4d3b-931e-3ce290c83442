import { Timestamp } from 'firebase/firestore';
import { 
  RecurringFrequency, 
  MaintenanceContractStatus, 
  PaymentStatus,
  MaintenanceContract,
  RecurringPayment,
  SupportingDocument,
  MaintenanceContractInput,
  RecurringPaymentInput,
  MaintenanceContractFilters,
  RecurringPaymentFilters
} from './project';

// Notification Types
export type NotificationType = 'payment_due' | 'payment_overdue' | 'contract_expiring' | 'payment_received';

export interface PaymentNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  recipientId: string;
  recipientEmail: string;
  relatedEntityId: string; // Contract ID or Payment ID
  relatedEntityType: 'maintenance_contract' | 'recurring_payment';
  isRead: boolean;
  sentAt?: Timestamp;
  createdAt: Timestamp;
}

// File Upload Types
export interface FileUploadRequest {
  file: File;
  description?: string;
  recurringPaymentId: string;
}

export interface FileUploadResponse {
  success: boolean;
  document?: SupportingDocument;
  error?: string;
}

// Dashboard Summary Types
export interface RecurringPaymentSummary {
  totalActiveContracts: number;
  totalMonthlyRevenue: number;
  upcomingPayments: number;
  overduePayments: number;
  paymentsThisMonth: number;
  revenueThisMonth: number;
  contractsExpiringThisMonth: number;
}

export interface UpcomingPayment {
  id: string;
  contractName: string;
  projectName: string;
  clientName: string;
  amount: number;
  currency: string;
  dueDate: Timestamp;
  daysUntilDue: number;
  status: PaymentStatus;
}

export interface OverduePayment extends UpcomingPayment {
  daysOverdue: number;
  overdueAt: Timestamp;
}

// Service Interface for Recurring Payments
export interface RecurringPaymentService {
  // Maintenance Contracts
  createMaintenanceContract: (contract: MaintenanceContractInput, createdBy: string) => Promise<string>;
  updateMaintenanceContract: (id: string, updates: Partial<MaintenanceContract>) => Promise<void>;
  deleteMaintenanceContract: (id: string) => Promise<void>;
  getMaintenanceContract: (id: string) => Promise<MaintenanceContract | null>;
  listMaintenanceContracts: (filters: MaintenanceContractFilters, limit?: number, offset?: number) => Promise<any>;
  
  // Recurring Payments
  createRecurringPayment: (payment: RecurringPaymentInput, createdBy: string) => Promise<string>;
  updateRecurringPayment: (id: string, updates: Partial<RecurringPayment>) => Promise<void>;
  markPaymentAsPaid: (id: string, paidBy: string, supportingDocuments?: File[]) => Promise<void>;
  markPaymentAsOverdue: (id: string) => Promise<void>;
  getRecurringPayment: (id: string) => Promise<RecurringPayment | null>;
  listRecurringPayments: (filters: RecurringPaymentFilters, limit?: number, offset?: number) => Promise<any>;
  
  // File Management
  uploadSupportingDocument: (request: FileUploadRequest, uploadedBy: string) => Promise<SupportingDocument>;
  deleteSupportingDocument: (documentId: string, recurringPaymentId: string) => Promise<void>;
  downloadSupportingDocument: (documentId: string) => Promise<Blob>;
  
  // Dashboard & Analytics
  getRecurringPaymentSummary: () => Promise<RecurringPaymentSummary>;
  getUpcomingPayments: (days?: number) => Promise<UpcomingPayment[]>;
  getOverduePayments: () => Promise<OverduePayment[]>;
  
  // Notifications
  sendPaymentReminder: (paymentId: string) => Promise<void>;
  sendOverdueNotification: (paymentId: string) => Promise<void>;
  markNotificationAsRead: (notificationId: string) => Promise<void>;
}

// Cloud Function Request/Response Types
export interface CreateMaintenanceContractRequest {
  contract: MaintenanceContractInput;
}

export interface UpdateMaintenanceContractRequest {
  contractId: string;
  updates: Partial<MaintenanceContract>;
}

export interface MarkPaymentAsPaidRequest {
  paymentId: string;
  supportingDocuments?: {
    fileName: string;
    fileData: string; // base64 encoded
    mimeType: string;
    description?: string;
  }[];
}

export interface ProcessScheduledPaymentsRequest {
  // This will be called by scheduled function
  dryRun?: boolean;
}

export interface ProcessScheduledPaymentsResponse {
  processed: number;
  created: number;
  markedOverdue: number;
  errors: string[];
}

// Utility Types
export interface PaymentSchedule {
  contractId: string;
  nextPaymentDate: Date;
  amount: number;
  currency: string;
}

export interface ContractMetrics {
  contractId: string;
  totalPayments: number;
  totalAmount: number;
  averagePaymentTime: number; // days
  onTimePaymentRate: number; // percentage
  lastPaymentDate?: Date;
}
