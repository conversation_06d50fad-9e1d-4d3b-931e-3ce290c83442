import { Timestamp } from 'firebase/firestore';

export type ActivityAction = 
  | 'user_login' 
  | 'user_logout' 
  | 'profile_update'
  | 'dashboard_access'
  | 'admin_panel_access'
  | 'user_management_access'
  | 'role_change'
  | 'user_status_change'
  | 'user_suspended'
  | 'user_activated'
  | 'user_creation'
  | 'settings_access'
  | 'activity_logs_access'
  | 'projects_access'
  | 'project_created'
  | 'project_updated'
  | 'project_deleted'
  | 'project_status_change'
  | 'ticket_created'
  | 'ticket_updated'
  | 'ticket_deleted'
  | 'ticket_status_change'
  | 'payment_created'
  | 'payment_updated'
  | 'payment_deleted'
  | 'payment_status_change'
  | 'invoice_generated'
  | 'invoice_updated'
  | 'invoice_deleted'
  | 'invoice_status_change';

export interface ActivityMetadata {
  userAgent?: string;
  pathname?: string;
  sessionId?: string;
  ipAddress?: string;
}

export interface ActivityLog {
  id: string;
  userId: string;
  userEmail: string;
  userName: string;
  action: ActivityAction;
  details: Record<string, any>;
  timestamp: Timestamp | any;
  metadata: ActivityMetadata;
}

export interface ActivityLogInput {
  action: ActivityAction;
  details?: Record<string, any>;
  metadata?: Partial<ActivityMetadata>;
}

export interface ActivityFilters {
  userId?: string;
  action?: ActivityAction;
  dateFrom?: Date;
  dateTo?: Date;
  searchTerm?: string;
}

export interface ActivityLogResponse {
  logs: ActivityLog[];
  total: number;
  hasMore: boolean;
  limit: number;
  offset: number;
}

export interface ActivityStats {
  totalActivities: number;
  todayActivities: number;
  uniqueUsers: number;
  mostActiveUser: {
    userId: string;
    userName: string;
    count: number;
  } | null;
  actionBreakdown: Record<ActivityAction, number>;
  loading: boolean;
  error: string | null;
} 