import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date));
}

export function formatDateTime(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
}

// Generate structured IDs starting with "ONE-"
export function generateStructuredId(prefix: string = ''): string {
  const timestamp = Date.now().toString(36).toUpperCase();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  const fullPrefix = prefix ? `${prefix}-` : '';
  return `ONE-${fullPrefix}${timestamp}-${random}`;
}

// Generate project ID
export function generateProjectId(): string {
  return generateStructuredId('PRJ');
}

// Generate ticket ID
export function generateTicketId(): string {
  return generateStructuredId('TKT');
}

// Generate payment ID
export function generatePaymentId(): string {
  return generateStructuredId('PAY');
}

// Generate maintenance contract ID
export function generateMaintenanceContractId(): string {
  return generateStructuredId('MCT');
}

// Generate recurring payment ID
export function generateRecurringPaymentId(): string {
  return generateStructuredId('RPY');
}

// Generate supporting document ID
export function generateSupportingDocumentId(): string {
  return generateStructuredId('DOC');
}

// Generate notification ID
export function generateNotificationId(): string {
  return generateStructuredId('NOT');
}

// Generate client association ID
export function generateClientId(): string {
  return generateStructuredId('CLT');
}

// Resolve user display name from UID
export async function resolveUserName(uid: string, usersCache?: Map<string, string>): Promise<string> {
  if (!uid) return 'Unknown';
  
  // Check cache first
  if (usersCache?.has(uid)) {
    return usersCache.get(uid) || 'Unknown';
  }
  
  try {
    const { getDoc, doc } = await import('firebase/firestore');
    const { db } = await import('./firebase');
    
    const userDoc = await getDoc(doc(db, 'users', uid));
    if (userDoc.exists()) {
      const userData = userDoc.data();
      const displayName = userData.displayName || userData.email || 'Unknown';
      
      // Update cache if provided
      if (usersCache) {
        usersCache.set(uid, displayName);
      }
      
      return displayName;
    }
    
    return 'Unknown';
  } catch (error) {
    console.error('Error resolving user name:', error);
    return 'Unknown';
  }
}

// Create a users cache for efficient lookups
export function createUsersCache(): Map<string, string> {
  return new Map<string, string>();
} 