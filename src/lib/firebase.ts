import { initializeApp, getApps } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getFunctions } from 'firebase/functions';
import { getStorage } from 'firebase/storage';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || 'AIzaSyDdCTuvSd06d9aTxDJjhPIrx27P9FMgT-0',
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || 'agnextech.firebaseapp.com',
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'agnextech',
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'agnextech.firebasestorage.app',
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '710557068954',
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '1:710557068954:web:7893d2a889250c272d7923',
  measurementId: 'G-03NF7QCJFL'
};

// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const functions = getFunctions(app);
export const storage = getStorage(app);

// Connect to emulators in development - DISABLED for now to use production
// if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
//   try {
//     if (!auth._delegate._config?.emulator) {
//       connectAuthEmulator(auth, 'http://localhost:9099');
//     }
//     if (!db._delegate._databaseId?.projectId?.includes('demo-')) {
//       connectFirestoreEmulator(db, 'localhost', 8080);
//     }
//     if (!functions._delegate._url?.includes('localhost')) {
//       connectFunctionsEmulator(functions, 'localhost', 5001);
//     }
//   } catch (error) {
//     // Ignore emulator connection errors during build
//     console.warn('Could not connect to emulators:', error);
//   }
// }

export default app; 