import { 
  collection, 
  updateDoc,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  query, 
  orderBy, 
  limit as firestoreLimit, 
  where,
  serverTimestamp,
  Timestamp,
  setDoc
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { 
  generateProjectId, 
  generateTicketId, 
  generatePaymentId, 
  generateClientId,
  resolveUserName,
  createUsersCache
} from '@/lib/utils';
import { 
  Project,
  ProjectClient,
  Ticket,
  Payment,
  ProjectListItem,
  TicketListItem,
  PaymentListItem,
  ProjectListResponse,
  TicketListResponse,
  PaymentListResponse,
  ProjectFilters,
  TicketFilters,
  PaymentFilters,
  ProjectInput,
  TicketInput,
  PaymentInput
} from '@/types/project';

export class ProjectManagementService {
  // =============================================================================
  // PROJECT OPERATIONS
  // =============================================================================

  // Create a new project
  static async createProject(
    projectData: ProjectInput,
    createdBy: string
  ): Promise<string> {
    try {
      const projectId = generateProjectId();
      const project = {
        name: projectData.name,
        description: projectData.description,
        status: projectData.status,
        totalCost: projectData.totalCost,
        currency: projectData.currency,
        startDate: Timestamp.fromDate(projectData.startDate),
        endDate: projectData.endDate ? Timestamp.fromDate(projectData.endDate) : null,
        createdBy,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      await setDoc(doc(db, 'projects', projectId), project);
      
      // Initialize financial summary with zero values
      const initialFinancialSummary = {
        projectId,
        totalCost: projectData.totalCost,
        totalPaid: 0, // Start with zero paid
        regularPayments: 0,
        changeRequestPayments: 0,
        totalPending: 0,
        totalOverdue: 0,
        totalCancelled: 0,
        paymentProgress: 0,
        baseProjectCost: projectData.totalCost, // Original cost
        totalChangeRequestCost: 0,
        paymentsCount: 0,
        lastCalculatedAt: serverTimestamp(),
        autoCalculated: true
      };

      await setDoc(doc(db, 'projectFinancialSummaries', projectId), initialFinancialSummary);
      
      return projectId;
    } catch (error: any) {
      throw new Error(`Failed to create project: ${error.message}`);
    }
  }

  // Update an existing project
  static async updateProject(id: string, updates: Partial<Project>): Promise<void> {
    try {
      const updateData: any = {
        ...updates,
        updatedAt: serverTimestamp(),
      };

      // Remove undefined values to avoid Firestore errors
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      await updateDoc(doc(db, 'projects', id), updateData);
    } catch (error: any) {
      throw new Error(`Failed to update project: ${error.message}`);
    }
  }

  // Check project relationships before deletion
  static async checkProjectRelationships(id: string): Promise<{
    canDelete: boolean;
    blockers: string[];
    ticketCount: number;
    paymentCount: number;
  }> {
    try {
      // Check for linked tickets
      const ticketQuery = query(collection(db, 'tickets'), where('projectId', '==', id));
      const ticketSnapshot = await getDocs(ticketQuery);
      
      // Check for linked payments
      const paymentQuery = query(collection(db, 'payments'), where('projectId', '==', id));
      const paymentSnapshot = await getDocs(paymentQuery);
      
      const blockers: string[] = [];
      if (ticketSnapshot.size > 0) {
        blockers.push(`${ticketSnapshot.size} ticket(s)`);
      }
      if (paymentSnapshot.size > 0) {
        blockers.push(`${paymentSnapshot.size} payment(s)`);
      }
      
      return {
        canDelete: blockers.length === 0,
        blockers,
        ticketCount: ticketSnapshot.size,
        paymentCount: paymentSnapshot.size,
      };
    } catch (error: any) {
      throw new Error(`Failed to check project relationships: ${error.message}`);
    }
  }

  // Delete a project
  static async deleteProject(id: string): Promise<void> {
    try {
      // Check relationships first
      const relationshipCheck = await this.checkProjectRelationships(id);
      if (!relationshipCheck.canDelete) {
        throw new Error(`Cannot delete project. It has linked ${relationshipCheck.blockers.join(' and ')}. Please delete these first.`);
      }
      
      // Delete the project document
      await deleteDoc(doc(db, 'projects', id));
      
      // Clean up related projectFinancialSummaries document
      try {
        await deleteDoc(doc(db, 'projectFinancialSummaries', id));
      } catch (summaryError) {
        // If financial summary doesn't exist, that's fine - just log it
        console.warn(`No financial summary found for project ${id}:`, summaryError);
      }
      
      // Clean up any project clients relationships
      try {
        const projectClientsQuery = query(collection(db, 'projectClients'), where('projectId', '==', id));
        const projectClientsSnapshot = await getDocs(projectClientsQuery);
        
        const deletePromises = projectClientsSnapshot.docs.map(clientDoc => 
          deleteDoc(doc(db, 'projectClients', clientDoc.id))
        );
        
        if (deletePromises.length > 0) {
          await Promise.all(deletePromises);
        }
      } catch (clientsError) {
        console.warn(`Error cleaning up project clients for project ${id}:`, clientsError);
      }
      
    } catch (error: any) {
      throw new Error(`Failed to delete project: ${error.message}`);
    }
  }

  // Get projects with filtering and pagination (enhanced with user name resolution)
  static async getProjects(params: {
    limit?: number;
    filters?: ProjectFilters;
  } = {}): Promise<ProjectListResponse> {
    try {
      const { limit = 50, filters = {} } = params;
      const { 
        status, 
        createdBy, 
        searchTerm,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = filters;

      // Build query
      let baseQuery = collection(db, 'projects');
      let queryConstraints: any[] = [];

      // Apply filters
      if (status) {
        queryConstraints.push(where('status', '==', status));
      }
      if (createdBy) {
        queryConstraints.push(where('createdBy', '==', createdBy));
      }

      // Apply sorting
      queryConstraints.push(orderBy(sortBy, sortOrder));
      queryConstraints.push(firestoreLimit(limit));

      const q = query(baseQuery, ...queryConstraints);
      const snapshot = await getDocs(q);
      
      const projects: ProjectListItem[] = [];
      const usersCache = createUsersCache();
      
      for (const docSnap of snapshot.docs) {
        const data = docSnap.data();
        const projectId = docSnap.id;
        
        // Apply search filter if provided
        if (searchTerm) {
          const searchLower = searchTerm.toLowerCase();
          const name = (data.name || '').toLowerCase();
          const description = (data.description || '').toLowerCase();
          
          if (!name.includes(searchLower) && !description.includes(searchLower)) {
            continue;
          }
        }

        // Get client count for this project
        const clientQuery = query(collection(db, 'projectClients'), where('projectId', '==', projectId));
        const clientSnapshot = await getDocs(clientQuery);
        
        // Get ticket count for this project
        const ticketQuery = query(collection(db, 'tickets'), where('projectId', '==', projectId));
        const ticketSnapshot = await getDocs(ticketQuery);

        // Resolve creator name
        const createdByName = await resolveUserName(data.createdBy, usersCache);

        projects.push({
          id: projectId,
          name: data.name || '',
          description: data.description || '',
          status: data.status || 'draft',
          totalCost: data.totalCost || data.budget || 0, // Backward compatibility
          currency: data.currency || 'INR',
          startDate: data.startDate,
          endDate: data.endDate,
          createdBy: data.createdBy || '',
          createdByName,
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
          clientCount: clientSnapshot.size,
          ticketCount: ticketSnapshot.size,
        });
      }

      return {
        projects,
        total: projects.length,
        hasMore: projects.length === limit,
        limit,
        offset: 0,
      };
    } catch (error: any) {
      throw new Error(`Failed to get projects: ${error.message}`);
    }
  }

  // =============================================================================
  // TICKET OPERATIONS
  // =============================================================================

  // Create a new ticket
  static async createTicket(
    ticketData: TicketInput,
    createdBy: string
  ): Promise<string> {
    try {
      const ticketId = generateTicketId();
      const ticket = {
        projectId: ticketData.projectId,
        title: ticketData.title,
        description: ticketData.description,
        type: ticketData.type,
        priority: ticketData.priority,
        status: 'open' as const,
        assignedTo: ticketData.assignedTo,
        createdBy,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      await setDoc(doc(db, 'tickets', ticketId), ticket);
      return ticketId;
    } catch (error: any) {
      throw new Error(`Failed to create ticket: ${error.message}`);
    }
  }

  // Update an existing ticket
  static async updateTicket(id: string, updates: Partial<Ticket>): Promise<void> {
    try {
      const updateData: any = {
        ...updates,
        updatedAt: serverTimestamp(),
      };

      // Set completedAt when status changes to completed
      if (updates.status === 'completed' && !updates.completedAt) {
        updateData.completedAt = serverTimestamp();
      }

      // Remove undefined values to avoid Firestore errors
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      await updateDoc(doc(db, 'tickets', id), updateData);
    } catch (error: any) {
      throw new Error(`Failed to update ticket: ${error.message}`);
    }
  }

  // Check ticket relationships before deletion
  static async checkTicketRelationships(id: string): Promise<{
    canDelete: boolean;
    blockers: string[];
    paymentCount: number;
  }> {
    try {
      // Check for linked payments
      const paymentQuery = query(collection(db, 'payments'), where('linkedTicketId', '==', id));
      const paymentSnapshot = await getDocs(paymentQuery);
      
      const blockers: string[] = [];
      if (paymentSnapshot.size > 0) {
        blockers.push(`${paymentSnapshot.size} linked payment(s)`);
      }
      
      return {
        canDelete: blockers.length === 0,
        blockers,
        paymentCount: paymentSnapshot.size,
      };
    } catch (error: any) {
      throw new Error(`Failed to check ticket relationships: ${error.message}`);
    }
  }

  // Delete a ticket
  static async deleteTicket(id: string): Promise<void> {
    try {
      // Check relationships first
      const relationshipCheck = await this.checkTicketRelationships(id);
      if (!relationshipCheck.canDelete) {
        throw new Error(`Cannot delete ticket. It has ${relationshipCheck.blockers.join(' and ')}. Please delete these first.`);
      }
      
      await deleteDoc(doc(db, 'tickets', id));
    } catch (error: any) {
      throw new Error(`Failed to delete ticket: ${error.message}`);
    }
  }

  // Get tickets with filtering (enhanced with user name resolution)
  static async getTickets(params: {
    limit?: number;
    filters?: TicketFilters;
  } = {}): Promise<TicketListResponse> {
    try {
      const { limit = 50, filters = {} } = params;
      const { 
        projectId,
        type,
        status,
        priority,
        assignedTo,
        createdBy,
        searchTerm,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = filters;

      let baseQuery = collection(db, 'tickets');
      let queryConstraints: any[] = [];

      // Apply filters
      if (projectId) {
        queryConstraints.push(where('projectId', '==', projectId));
      }
      if (type) {
        queryConstraints.push(where('type', '==', type));
      }
      if (status) {
        queryConstraints.push(where('status', '==', status));
      }
      if (priority) {
        queryConstraints.push(where('priority', '==', priority));
      }
      if (assignedTo) {
        queryConstraints.push(where('assignedTo', '==', assignedTo));
      }
      if (createdBy) {
        queryConstraints.push(where('createdBy', '==', createdBy));
      }

      // Apply sorting and limit
      queryConstraints.push(orderBy(sortBy, sortOrder));
      queryConstraints.push(firestoreLimit(limit));

      const q = query(baseQuery, ...queryConstraints);
      const snapshot = await getDocs(q);
      
      const tickets: TicketListItem[] = [];
      const usersCache = createUsersCache();
      
      // First get all project names
      const projectsCache = new Map<string, string>();
      
      for (const docSnap of snapshot.docs) {
        const data = docSnap.data();
        
        if (searchTerm) {
          const searchLower = searchTerm.toLowerCase();
          const title = (data.title || '').toLowerCase();
          const description = (data.description || '').toLowerCase();
          
          if (!title.includes(searchLower) && !description.includes(searchLower)) {
            continue;
          }
        }

        // Get project name if not cached
        let projectName = projectsCache.get(data.projectId) || 'Unknown Project';
        if (projectName === 'Unknown Project') {
          try {
            const projectDoc = await getDoc(doc(db, 'projects', data.projectId));
            projectName = projectDoc.exists() ? projectDoc.data()?.name || 'Unknown Project' : 'Unknown Project';
            projectsCache.set(data.projectId, projectName);
          } catch {
            projectName = 'Unknown Project';
          }
        }

        // Resolve user names
        const createdByName = await resolveUserName(data.createdBy, usersCache);
        const assignedToName = await resolveUserName(data.assignedTo, usersCache);

        tickets.push({
          id: docSnap.id,
          projectId: data.projectId || '',
          projectName,
          title: data.title || '',
          description: data.description || '',
          type: data.type || 'development',
          priority: data.priority || 'medium',
          status: data.status || 'open',
          assignedTo: data.assignedTo || '',
          assignedToName,
          createdBy: data.createdBy || '',
          createdByName,
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
          completedAt: data.completedAt,
        });
      }

      return {
        tickets,
        total: tickets.length,
        hasMore: tickets.length === limit,
        limit,
        offset: 0,
      };
    } catch (error: any) {
      throw new Error(`Failed to get tickets: ${error.message}`);
    }
  }

  // =============================================================================
  // PAYMENT OPERATIONS
  // =============================================================================

  // Create a new payment with enhanced payment type support
  static async createPayment(
    paymentData: PaymentInput,
    createdBy: string
  ): Promise<string> {
    try {
      const paymentId = generatePaymentId();
      const payment = {
        projectId: paymentData.projectId,
        clientId: paymentData.clientId,
        amount: paymentData.amount,
        currency: paymentData.currency,
        status: 'pending' as const,
        paymentType: paymentData.paymentType || 'regular',
  
        description: paymentData.description || null,
        linkedTicketId: paymentData.linkedTicketId || null,
        notes: paymentData.notes || null,
        createdBy,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      await setDoc(doc(db, 'payments', paymentId), payment);
      return paymentId;
    } catch (error: any) {
      throw new Error(`Failed to create payment: ${error.message}`);
    }
  }

  // Update an existing payment
  static async updatePayment(id: string, updates: Partial<Payment>): Promise<void> {
    try {
      const updateData: any = {
        ...updates,
        updatedAt: serverTimestamp(),
      };

      // Set paidAt when status changes to paid
      if (updates.status === 'paid' && !updates.paidAt) {
        updateData.paidAt = serverTimestamp();
      }

      // Remove undefined values to avoid Firestore errors
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      await updateDoc(doc(db, 'payments', id), updateData);
    } catch (error: any) {
      throw new Error(`Failed to update payment: ${error.message}`);
    }
  }

  // Delete a payment (with automatic invoice and PDF cleanup)
  static async deletePayment(id: string): Promise<void> {
    try {
      // Use the cloud function for secure deletion with full cleanup
      const { httpsCallable } = await import('firebase/functions');
      const { functions } = await import('@/lib/firebase');
      
      const deletePaymentFn = httpsCallable(functions, 'deletePayment');
      await deletePaymentFn({ paymentId: id });
    } catch (error: any) {
      // Fallback to direct deletion if cloud function fails
      if (error.code === 'functions/not-found' || error.message?.includes('not-found')) {
        console.warn('Cloud function not available, falling back to direct deletion');
        await this.deletePaymentDirect(id);
      } else {
        throw new Error(`Failed to delete payment: ${error.message}`);
      }
    }
  }

  // Direct deletion method (fallback)
  private static async deletePaymentDirect(id: string): Promise<void> {
    try {
      // First, get the payment document to check for associated invoice
      const paymentDoc = await getDoc(doc(db, 'payments', id));
      
      if (!paymentDoc.exists()) {
        throw new Error('Payment not found');
      }

      const paymentData = paymentDoc.data();
      const invoiceId = paymentData?.invoiceId;

      // If payment has an associated invoice, delete it and its PDF
      if (invoiceId) {
        await this.deleteInvoiceAndPDF(invoiceId);
      }

      // Delete the payment document
      await deleteDoc(doc(db, 'payments', id));
    } catch (error: any) {
      throw new Error(`Failed to delete payment: ${error.message}`);
    }
  }

  // Helper method to delete invoice and its PDF from Firebase Storage
  private static async deleteInvoiceAndPDF(invoiceId: string): Promise<void> {
    try {
      // Get invoice document to get invoice number for PDF file path
      const invoiceDoc = await getDoc(doc(db, 'invoices', invoiceId));
      
      if (invoiceDoc.exists()) {
        const invoiceData = invoiceDoc.data();
        const invoiceNumber = invoiceData?.invoiceNumber;

        // Delete PDF from Firebase Storage if it exists
        if (invoiceNumber) {
          try {
            // Import Firebase Storage functions dynamically to avoid issues
            const { ref, deleteObject } = await import('firebase/storage');
            const { storage } = await import('@/lib/firebase');
            
            const pdfPath = `invoices/${invoiceId}/invoice-${invoiceNumber}.pdf`;
            const pdfRef = ref(storage, pdfPath);
            
            // Attempt to delete the PDF file (won't fail if file doesn't exist)
            await deleteObject(pdfRef);
          } catch (storageError) {
            // Log warning but don't fail the entire operation if PDF deletion fails
            console.warn(`Failed to delete PDF for invoice ${invoiceId}:`, storageError);
          }
        }

        // Delete the invoice document from Firestore
        await deleteDoc(doc(db, 'invoices', invoiceId));
      }
    } catch (error) {
      // Log warning but don't fail the payment deletion if invoice cleanup fails
      console.warn(`Failed to clean up invoice ${invoiceId}:`, error);
    }
  }

  // Get payments with filtering (enhanced with user name resolution)
  static async getPayments(params: {
    limit?: number;
    filters?: PaymentFilters;
  } = {}): Promise<PaymentListResponse> {
    try {
      const { limit = 50, filters = {} } = params;
      const { 
        projectId,
        clientId,
        status,
        searchTerm,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = filters;

      let baseQuery = collection(db, 'payments');
      let queryConstraints: any[] = [];

      // Apply filters
      if (projectId) {
        queryConstraints.push(where('projectId', '==', projectId));
      }
      if (clientId) {
        queryConstraints.push(where('clientId', '==', clientId));
      }
      if (status) {
        queryConstraints.push(where('status', '==', status));
      }

      // Apply sorting and limit
      queryConstraints.push(orderBy(sortBy, sortOrder));
      queryConstraints.push(firestoreLimit(limit));

      const q = query(baseQuery, ...queryConstraints);
      const snapshot = await getDocs(q);
      
      const payments: PaymentListItem[] = [];
      const usersCache = createUsersCache();
      const projectsCache = new Map<string, string>();
      const clientsCache = new Map<string, string>();
      
      for (const docSnap of snapshot.docs) {
        const data = docSnap.data();
        
        if (searchTerm) {
          const searchLower = searchTerm.toLowerCase();
          const description = (data.description || '').toLowerCase();
          const notes = (data.notes || '').toLowerCase();
          
          if (!description.includes(searchLower) && !notes.includes(searchLower)) {
            continue;
          }
        }

        // Get project name if not cached
        let projectName = projectsCache.get(data.projectId) || 'Unknown Project';
        if (projectName === 'Unknown Project') {
          try {
            const projectDoc = await getDoc(doc(db, 'projects', data.projectId));
            projectName = projectDoc.exists() ? projectDoc.data()?.name || 'Unknown Project' : 'Unknown Project';
            projectsCache.set(data.projectId, projectName);
          } catch {
            projectName = 'Unknown Project';
          }
        }

        // Get client name if not cached
        let clientName = clientsCache.get(data.clientId);
        if (!clientName) {
          clientName = await resolveUserName(data.clientId, usersCache);
          clientsCache.set(data.clientId, clientName);
        }

        // Resolve creator name
        const createdByName = await resolveUserName(data.createdBy, usersCache);

        payments.push({
          id: docSnap.id,
          projectId: data.projectId || '',
          projectName,
          clientId: data.clientId || '',
          clientName,
          amount: data.amount || 0,
          currency: data.currency || 'INR',
          status: data.status || 'pending',
          paymentType: data.paymentType || 'regular',
  
          description: data.description,
          paidAt: data.paidAt,
          linkedTicketId: data.linkedTicketId,
          notes: data.notes,
          createdBy: data.createdBy || '',
          createdByName,
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
        });
      }

      return {
        payments,
        total: payments.length,
        hasMore: payments.length === limit,
        limit,
        offset: 0,
      };
    } catch (error: any) {
      throw new Error(`Failed to get payments: ${error.message}`);
    }
  }

  // =============================================================================
  // PROJECT CLIENT OPERATIONS
  // =============================================================================

  // Add a client to a project
  static async addProjectClient(
    projectId: string,
    clientId: string,
    addedBy: string
  ): Promise<string> {
    try {
      const projectClientId = generateClientId();
      const projectClient = {
        projectId,
        clientId,
        addedBy,
        addedAt: serverTimestamp(),
      };

      await setDoc(doc(db, 'projectClients', projectClientId), projectClient);
      return projectClientId;
    } catch (error: any) {
      throw new Error(`Failed to add project client: ${error.message}`);
    }
  }

  // Get clients for a project
  static async getProjectClients(projectId: string): Promise<ProjectClient[]> {
    try {
      const q = query(
        collection(db, 'projectClients'),
        where('projectId', '==', projectId)
      );
      
      const snapshot = await getDocs(q);
      const clients: ProjectClient[] = [];
      
      snapshot.docs.forEach((doc) => {
        const data = doc.data();
        clients.push({
          id: doc.id,
          projectId: data.projectId,
          clientId: data.clientId,
          addedBy: data.addedBy,
          addedAt: data.addedAt,
          isPrimary: data.isPrimary || false,
        });
      });
      
      return clients;
    } catch (error: any) {
      throw new Error(`Failed to get project clients: ${error.message}`);
    }
  }

  // Remove a client from a project
  static async removeProjectClient(projectClientId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'projectClients', projectClientId));
    } catch (error: any) {
      throw new Error(`Failed to remove project client: ${error.message}`);
    }
  }

  // Set primary client for a project
  static async setPrimaryProjectClient(projectId: string, clientId: string): Promise<void> {
    try {
      // First, remove primary status from all clients in this project
      const q = query(
        collection(db, 'projectClients'),
        where('projectId', '==', projectId)
      );
      
      const snapshot = await getDocs(q);
      const updatePromises = snapshot.docs.map(async (docSnap) => {
        const data = docSnap.data();
        const isPrimary = data.clientId === clientId;
        return updateDoc(doc(db, 'projectClients', docSnap.id), { isPrimary });
      });
      
      await Promise.all(updatePromises);
    } catch (error: any) {
      throw new Error(`Failed to set primary project client: ${error.message}`);
    }
  }
} 