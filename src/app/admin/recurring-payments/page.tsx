'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { RecurringPaymentsDashboard } from '@/components/admin/recurring/RecurringPaymentsDashboard';
import { MaintenanceContractsList } from '@/components/admin/recurring/MaintenanceContractsList';
import { RecurringPaymentsList } from '@/components/admin/recurring/RecurringPaymentsList';
import { MaintenanceContractForm } from '@/components/admin/forms/MaintenanceContractForm';
import { FileUploadModal } from '@/components/admin/recurring/FileUploadModal';
import { RecurringPaymentService } from '@/services/recurringPaymentService';
import { 
  MaintenanceContractInput,
  MaintenanceContractListItem,
  RecurringPaymentListItem
} from '@/types/recurring-payments';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function RecurringPaymentsPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showContractForm, setShowContractForm] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [editingContract, setEditingContract] = useState<MaintenanceContractListItem | null>(null);
  const [selectedPayment, setSelectedPayment] = useState<RecurringPaymentListItem | null>(null);
  const [contractFormLoading, setContractFormLoading] = useState(false);

  useEffect(() => {
    if (!loading && (!user || user.role !== 'admin')) {
      router.push('/dashboard');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return null;
  }

  const handleCreateContract = () => {
    setEditingContract(null);
    setShowContractForm(true);
  };

  const handleEditContract = (contract: MaintenanceContractListItem) => {
    setEditingContract(contract);
    setShowContractForm(true);
  };

  const handleViewContract = (contract: MaintenanceContractListItem) => {
    // TODO: Implement contract detail view
    toast.info('Contract detail view coming soon');
  };

  const handleContractSubmit = async (data: MaintenanceContractInput) => {
    setContractFormLoading(true);
    try {
      if (editingContract) {
        await RecurringPaymentService.updateMaintenanceContract(editingContract.id, data);
        toast.success('Maintenance contract updated successfully');
      } else {
        await RecurringPaymentService.createMaintenanceContract(data);
        toast.success('Maintenance contract created successfully');
      }
      setShowContractForm(false);
      setEditingContract(null);
    } catch (error) {
      console.error('Error saving contract:', error);
      toast.error('Failed to save maintenance contract');
    } finally {
      setContractFormLoading(false);
    }
  };

  const handleViewPayment = (payment: RecurringPaymentListItem) => {
    // TODO: Implement payment detail view
    toast.info('Payment detail view coming soon');
  };

  const handleMarkAsPaid = async (payment: RecurringPaymentListItem) => {
    try {
      await RecurringPaymentService.markPaymentAsPaid(payment.id);
      toast.success('Payment marked as paid successfully');
    } catch (error) {
      console.error('Error marking payment as paid:', error);
      toast.error('Failed to mark payment as paid');
    }
  };

  const handleUploadDocument = (payment: RecurringPaymentListItem) => {
    setSelectedPayment(payment);
    setShowFileUpload(true);
  };

  const handleFileUploadComplete = () => {
    setShowFileUpload(false);
    setSelectedPayment(null);
    toast.success('Documents uploaded successfully');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          <RecurringPaymentsDashboard
            onCreateContract={handleCreateContract}
            onViewContracts={() => setActiveTab('contracts')}
            onViewPayments={() => setActiveTab('payments')}
          />
        </TabsContent>

        <TabsContent value="contracts">
          <MaintenanceContractsList
            onCreateContract={handleCreateContract}
            onEditContract={handleEditContract}
            onViewContract={handleViewContract}
          />
        </TabsContent>

        <TabsContent value="payments">
          <RecurringPaymentsList
            onViewPayment={handleViewPayment}
            onMarkAsPaid={handleMarkAsPaid}
            onUploadDocument={handleUploadDocument}
          />
        </TabsContent>
      </Tabs>

      {/* Contract Form Modal */}
      <Dialog open={showContractForm} onOpenChange={setShowContractForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingContract ? 'Edit Maintenance Contract' : 'Create Maintenance Contract'}
            </DialogTitle>
          </DialogHeader>
          <MaintenanceContractForm
            onSubmit={handleContractSubmit}
            onCancel={() => setShowContractForm(false)}
            isLoading={contractFormLoading}
            initialData={editingContract ? {
              projectId: editingContract.projectId,
              clientId: editingContract.clientId,
              name: editingContract.name,
              description: editingContract.description,
              amount: editingContract.amount,
              currency: editingContract.currency,
              frequency: editingContract.frequency,
              startDate: editingContract.startDate.toDate(),
              endDate: editingContract.endDate?.toDate(),
              autoGenerateInvoices: editingContract.autoGenerateInvoices,
              reminderDaysBefore: editingContract.reminderDaysBefore,
              gracePeriodDays: editingContract.gracePeriodDays,
              notes: editingContract.notes,
            } : undefined}
          />
        </DialogContent>
      </Dialog>

      {/* File Upload Modal */}
      {selectedPayment && (
        <FileUploadModal
          isOpen={showFileUpload}
          onClose={() => setShowFileUpload(false)}
          recurringPaymentId={selectedPayment.id}
          onUploadComplete={handleFileUploadComplete}
        />
      )}
    </div>
  );
}
