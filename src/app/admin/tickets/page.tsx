'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useProjectManagement, ProjectManagementProvider } from '@/hooks/useProjectManagement';
import { useActivityLogger } from '@/hooks/useActivityLogger';
import { TicketStatus, TicketPriority, TicketType, Ticket } from '@/types/project';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { UnifiedHeader } from '@/components/admin/UnifiedHeader';
import { RelationshipIndicator } from '@/components/admin/RelationshipIndicator';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Search, 
  RefreshCw,
  Ticket as TicketIcon,
  CheckCircle,
  Clock,
  AlertCircle,
  Play,
  ExternalLink,
  User,
  AlertTriangle,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import Link from 'next/link';

function TicketsPageContent() {
  const { user } = useAuth();
  const { logActivity } = useActivityLogger();
  const {
    tickets,
    ticketsLoading,
    ticketFilters,
    setTicketFilters,
    loadTickets,
    updateTicket,
  } = useProjectManagement();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [updating, setUpdating] = useState<string | null>(null);

  // Load initial data
  useEffect(() => {
    loadTickets(true);
  }, [loadTickets]);

  // Update filters when search or filters change
  useEffect(() => {
    setTicketFilters({
      ...ticketFilters,
      searchTerm: searchTerm || undefined,
      status: statusFilter === 'all' ? undefined : statusFilter as TicketStatus,
      priority: priorityFilter === 'all' ? undefined : priorityFilter as TicketPriority,
      type: typeFilter === 'all' ? undefined : typeFilter as TicketType,
    });
  }, [searchTerm, statusFilter, priorityFilter, typeFilter]);

  const handleTicketStatusUpdate = async (ticketId: string, newStatus: TicketStatus) => {
    if (!user) return;
    
    setUpdating(ticketId);
    try {
      await updateTicket(ticketId, { status: newStatus });
      await logActivity('ticket_status_change', { ticketId, newStatus });
      toast.success(`Ticket status updated to ${newStatus}`);
    } catch (error) {
      console.error('Error updating ticket status:', error);
      toast.error('Failed to update ticket status');
    } finally {
      setUpdating(null);
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    try {
      let date: Date;
      if (timestamp instanceof Date) {
        date = timestamp;
      } else if (timestamp && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp && timestamp.seconds) {
        date = new Date(timestamp.seconds * 1000);
      } else {
        return 'N/A';
      }
      return format(date, 'MMM dd, yyyy');
    } catch {
      return 'N/A';
    }
  };

  const getStatusIcon = (status: TicketStatus) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in_progress': return <Play className="h-4 w-4 text-blue-500" />;
      case 'open': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'cancelled': return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusBadgeVariant = (status: TicketStatus): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'completed': return 'secondary';
      case 'in_progress': return 'default';
      case 'open': return 'outline';
      case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  const getPriorityIcon = (priority: TicketPriority) => {
    switch (priority) {
      case 'high': return <ArrowUp className="h-4 w-4 text-red-500" />;
      case 'medium': return <Minus className="h-4 w-4 text-yellow-500" />;
      case 'low': return <ArrowDown className="h-4 w-4 text-green-500" />;
      default: return <Minus className="h-4 w-4" />;
    }
  };

  const getPriorityBadgeVariant = (priority: TicketPriority): "default" | "secondary" | "destructive" | "outline" => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'outline';
    }
  };

  const getTypeColor = (type: TicketType) => {
    switch (type) {
      case 'development': return 'text-blue-600';
      case 'maintenance': return 'text-orange-600';
      case 'support': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  // Calculate summary stats
  const totalTickets = tickets.length;
  const openTickets = tickets.filter(t => t.status === 'open').length;
  const inProgressTickets = tickets.filter(t => t.status === 'in_progress').length;
  const completedTickets = tickets.filter(t => t.status === 'completed').length;
  const highPriorityTickets = tickets.filter(t => t.priority === 'high').length;

  return (
    <div className="space-y-6">
      <UnifiedHeader 
        title="Tickets"
        subtitle="Manage all tickets across all projects"
        actions={
          <Button 
            onClick={() => loadTickets(true)}
            disabled={ticketsLoading}
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${ticketsLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        }
        stats={[
          { label: 'Total', value: totalTickets, color: 'text-blue-600', icon: TicketIcon },
          { label: 'Open', value: openTickets, color: 'text-yellow-600', icon: Clock },
          { label: 'In Progress', value: inProgressTickets, color: 'text-blue-600', icon: Play },
          { label: 'Completed', value: completedTickets, color: 'text-green-600', icon: CheckCircle },
          { label: 'High Priority', value: highPriorityTickets, color: 'text-red-600', icon: AlertTriangle },
        ]}
      />



      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TicketIcon className="h-5 w-5" />
            All Tickets
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search tickets..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="All Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="development">Development</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="support">Support</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tickets Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Project</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Assigned To</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {ticketsLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                      <p className="text-muted-foreground">Loading tickets...</p>
                    </TableCell>
                  </TableRow>
                ) : tickets.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <p className="text-muted-foreground">No tickets found</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  tickets.map((ticket) => (
                    <TableRow key={ticket.id}>
                      <TableCell>
                        <div className="max-w-[200px]">
                          <div className="font-medium truncate">{ticket.title}</div>
                          <div className="text-sm text-muted-foreground truncate">
                            {ticket.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <RelationshipIndicator 
                          type="project"
                          data={{
                            id: ticket.projectId,
                            name: ticket.projectName,
                            status: 'active', // Default status since we don't have it
                          }}
                          compact={true}
                          showRelated={false}
                        />
                      </TableCell>
                      <TableCell>
                        <span className={`text-sm font-medium capitalize ${getTypeColor(ticket.type)}`}>
                          {ticket.type}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getPriorityBadgeVariant(ticket.priority)} className="flex items-center gap-1 w-fit">
                          {getPriorityIcon(ticket.priority)}
                          {ticket.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(ticket.status)} className="flex items-center gap-1 w-fit">
                          {getStatusIcon(ticket.status)}
                          {ticket.status.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 bg-muted rounded-full flex items-center justify-center">
                            <User className="h-4 w-4" />
                          </div>
                          <span className="text-sm">{ticket.assignedToName || 'Unassigned'}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {formatDate(ticket.createdAt)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {ticket.status === 'open' && (
                            <Button
                              size="sm"
                              onClick={() => handleTicketStatusUpdate(ticket.id, 'in_progress')}
                              disabled={updating === ticket.id}
                            >
                              {updating === ticket.id ? (
                                <RefreshCw className="h-3 w-3 animate-spin" />
                              ) : (
                                'Start'
                              )}
                            </Button>
                          )}
                          {ticket.status === 'in_progress' && (
                            <Button
                              size="sm"
                              onClick={() => handleTicketStatusUpdate(ticket.id, 'completed')}
                              disabled={updating === ticket.id}
                            >
                              {updating === ticket.id ? (
                                <RefreshCw className="h-3 w-3 animate-spin" />
                              ) : (
                                'Complete'
                              )}
                            </Button>
                          )}
                          {(ticket.status === 'open' || ticket.status === 'in_progress') && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleTicketStatusUpdate(ticket.id, 'cancelled')}
                              disabled={updating === ticket.id}
                            >
                              Cancel
                            </Button>
                          )}
                          <Button size="sm" variant="outline" asChild>
                            <Link href={`/admin/projects/${ticket.projectId}#tickets`}>
                              View
                            </Link>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function TicketsPage() {
  return (
    <ProjectManagementProvider>
      <AdminLayout>
        <TicketsPageContent />
      </AdminLayout>
    </ProjectManagementProvider>
  );
} 