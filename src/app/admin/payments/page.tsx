'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useProjectManagement, ProjectManagementProvider } from '@/hooks/useProjectManagement';
import { useActivityLogger } from '@/hooks/useActivityLogger';
import { PaymentStatus, Payment } from '@/types/project';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { UnifiedHeader } from '@/components/admin/UnifiedHeader';
import { RelationshipIndicator } from '@/components/admin/RelationshipIndicator';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardH<PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { 
  Search, 
  RefreshCw,
  CreditCard,
  CheckCircle,
  Clock,
  AlertCircle,
  DollarSign,
  FileText,
  ExternalLink
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import Link from 'next/link';

function PaymentsPageContent() {
  const { user } = useAuth();
  const { logActivity } = useActivityLogger();
  const {
    payments,
    paymentsLoading,
    paymentFilters,
    setPaymentFilters,
    loadPayments,
    updatePayment,
  } = useProjectManagement();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [updating, setUpdating] = useState<string | null>(null);

  // Load initial data
  useEffect(() => {
    loadPayments(true);
  }, [loadPayments]);

  // Update filters when search or status changes
  useEffect(() => {
    setPaymentFilters({
      ...paymentFilters,
      searchTerm: searchTerm || undefined,
      status: statusFilter === 'all' ? undefined : statusFilter as PaymentStatus,
    });
  }, [searchTerm, statusFilter]);

  const handlePaymentStatusUpdate = async (paymentId: string, newStatus: PaymentStatus) => {
    if (!user) return;
    
    setUpdating(paymentId);
    try {
      await updatePayment(paymentId, { status: newStatus });
      await logActivity('payment_status_change', { paymentId, newStatus });
      toast.success(`Payment status updated to ${newStatus}`);
    } catch (error) {
      console.error('Error updating payment status:', error);
      toast.error('Failed to update payment status');
    } finally {
      setUpdating(null);
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    try {
      let date: Date;
      if (timestamp instanceof Date) {
        date = timestamp;
      } else if (timestamp && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp && timestamp.seconds) {
        date = new Date(timestamp.seconds * 1000);
      } else {
        return 'N/A';
      }
      return format(date, 'MMM dd, yyyy');
    } catch {
      return 'N/A';
    }
  };

  const formatCurrency = (amount: number, currency = 'INR') => {
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount);
  };

  const getStatusIcon = (status: PaymentStatus) => {
    switch (status) {
      case 'paid': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'overdue': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled': return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusBadgeVariant = (status: PaymentStatus): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'paid': return 'secondary';
      case 'pending': return 'outline';
      case 'overdue': return 'destructive';
      case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  const getPaymentTypeColor = (type: string) => {
    return type === 'change_request' ? 'text-orange-600' : 'text-blue-600';
  };

  // Calculate summary stats
  const totalAmount = payments.reduce((sum, payment) => sum + payment.amount, 0);
  const paidAmount = payments.filter(p => p.status === 'paid').reduce((sum, payment) => sum + payment.amount, 0);
  const pendingAmount = payments.filter(p => p.status === 'pending').reduce((sum, payment) => sum + payment.amount, 0);
  const overdueAmount = payments.filter(p => p.status === 'overdue').reduce((sum, payment) => sum + payment.amount, 0);

  return (
    <div className="space-y-6">
      <UnifiedHeader 
        title="Payments"
        subtitle="Manage all payments across all projects"
        actions={
          <Button 
            onClick={() => loadPayments(true)}
            disabled={paymentsLoading}
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${paymentsLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        }
        stats={[
          { label: 'Total', value: formatCurrency(totalAmount), color: 'text-blue-600', icon: DollarSign },
          { label: 'Paid', value: formatCurrency(paidAmount), color: 'text-green-600', icon: CheckCircle },
          { label: 'Pending', value: formatCurrency(pendingAmount), color: 'text-yellow-600', icon: Clock },
          { label: 'Overdue', value: formatCurrency(overdueAmount), color: 'text-red-600', icon: AlertCircle },
        ]}
      />



      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            All Payments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search payments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Payments Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Project</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Paid Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paymentsLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                      <p className="text-muted-foreground">Loading payments...</p>
                    </TableCell>
                  </TableRow>
                ) : payments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <p className="text-muted-foreground">No payments found</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  payments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell>
                        <RelationshipIndicator 
                          type="project"
                          data={{
                            id: payment.projectId,
                            name: payment.projectName,
                            status: 'active', // Default status since we don't have it
                          }}
                          compact={true}
                          showRelated={false}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 bg-muted rounded-full flex items-center justify-center">
                            <span className="text-xs font-medium">
                              {payment.clientName?.charAt(0) || 'C'}
                            </span>
                          </div>
                          <span className="text-sm">{payment.clientName || 'Unknown Client'}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {formatCurrency(payment.amount, payment.currency)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={`text-sm font-medium ${getPaymentTypeColor(payment.paymentType)}`}>
                          {payment.paymentType === 'change_request' ? 'Change Request' : 'Regular'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(payment.status)} className="flex items-center gap-1 w-fit">
                          {getStatusIcon(payment.status)}
                          {payment.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {formatDate(payment.createdAt)}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {payment.paidAt ? formatDate(payment.paidAt) : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {payment.status === 'pending' && (
                            <Button
                              size="sm"
                              onClick={() => handlePaymentStatusUpdate(payment.id, 'paid')}
                              disabled={updating === payment.id}
                            >
                              {updating === payment.id ? (
                                <RefreshCw className="h-3 w-3 animate-spin" />
                              ) : (
                                'Mark Paid'
                              )}
                            </Button>
                          )}
                          {payment.status === 'pending' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handlePaymentStatusUpdate(payment.id, 'overdue')}
                              disabled={updating === payment.id}
                            >
                              Mark Overdue
                            </Button>
                          )}
                          {payment.invoiceId && (
                            <Button size="sm" variant="outline" asChild>
                              <Link href={`/admin/projects/${payment.projectId}#payments`}>
                                <FileText className="h-3 w-3 mr-1" />
                                Invoice
                              </Link>
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function PaymentsPage() {
  return (
    <ProjectManagementProvider>
      <AdminLayout>
        <PaymentsPageContent />
      </AdminLayout>
    </ProjectManagementProvider>
  );
} 