'use client';

import { useEffect } from 'react';
import { AdminGuard } from '@/components/auth/AuthGuard';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { RecentActivity } from '@/components/admin/RecentActivity';
import { WorkflowGuide } from '@/components/admin/WorkflowGuide';
import { RecurringPaymentsWidget } from '@/components/admin/dashboard/RecurringPaymentsWidget';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useActivityLogger } from '@/hooks/useActivityLogger';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAdminStats } from '@/hooks/useAdminStats';
import { 
  Users, 
  Shield, 
  Activity, 
  Settings,
  TrendingUp,
  Clock,
  AlertCircle,
  RefreshCw,
  FolderOpen,
  ArrowRight,
  Ticket,
  CreditCard
} from 'lucide-react';
import Link from 'next/link';

export default function AdminDashboardPage() {
  const { logDashboardAccess } = useActivityLogger();
  const { 
    totalUsers, 
    activeUsers, 
    adminUsers, 
    supportUsers,
    clientUsers,
    inactiveUsers,
    loading, 
    error, 
    refreshStats 
  } = useAdminStats();

  useEffect(() => {
    logDashboardAccess();
  }, [logDashboardAccess]);
  return (
    <AdminGuard>
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold">Admin Dashboard</h1>
              <p className="text-muted-foreground">
                Welcome to the Agnex One administration panel
              </p>
            </div>
            <Button
              onClick={refreshStats}
              disabled={loading}
              variant="outline"
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                  <span className="text-red-700">{error}</span>
                  <Button
                    onClick={refreshStats}
                    variant="outline"
                    size="sm"
                    className="ml-auto"
                  >
                    Try Again
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Overview Cards */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {loading ? '--' : error ? '⚠️' : totalUsers.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  <TrendingUp className="inline h-3 w-3 mr-1" />
                  {loading ? 'Loading...' : error ? 'Error loading' : 'All registered users'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {loading ? '--' : error ? '⚠️' : activeUsers.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  <Clock className="inline h-3 w-3 mr-1" />
                  {loading ? 'Loading...' : error ? 'Error loading' : `${inactiveUsers} inactive`}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Admin Users</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {loading ? '--' : error ? '⚠️' : adminUsers.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  <AlertCircle className="inline h-3 w-3 mr-1" />
                  {loading ? 'Loading...' : error ? 'Error loading' : `${supportUsers} support, ${clientUsers} clients`}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">System Status</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  <Badge variant="default">Online</Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  All systems operational
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  User Management
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Manage user accounts, roles, and permissions. View user activity and update account status.
                </p>
                <Link href="/admin/users">
                  <Button className="w-full">
                    Manage Users
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Activity Logs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Review system activity, user actions, and administrative changes for audit purposes.
                </p>
                <Link href="/admin/activity">
                  <Button className="w-full">
                    View Activity Logs
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  System Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Configure system-wide settings, security options, and application preferences.
                </p>
                <Link href="/admin/settings">
                  <Button variant="outline" className="w-full">
                    View Settings
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          {/* Workflow Guide */}
          <WorkflowGuide currentStep="projects" />

          {/* Project Management Section */}
          <div>
            <h2 className="text-2xl font-bold mb-4">Quick Access</h2>
            <div className="grid gap-6 md:grid-cols-3">
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FolderOpen className="h-5 w-5 mr-2 text-blue-600" />
                    Projects
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Complete project management with integrated tickets and payments. Track progress, budgets, timelines, and financial data.
                  </p>
                  <Link href="/admin/projects">
                    <Button className="w-full" variant="default">
                      Manage Projects
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Ticket className="h-5 w-5 mr-2 text-orange-600" />
                    Tickets
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    View and manage all tickets across all projects. Track development, maintenance, and support requests.
                  </p>
                  <Link href="/admin/tickets">
                    <Button className="w-full" variant="outline">
                      View All Tickets
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="h-5 w-5 mr-2 text-green-600" />
                    Payments
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Manage all payments across all projects. Track payment status, generate invoices, and view financial summaries.
                  </p>
                  <Link href="/admin/payments">
                    <Button className="w-full" variant="outline">
                      View All Payments
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Recurring Payments & Recent Activity */}
          <div className="grid gap-6 lg:grid-cols-2">
            <RecurringPaymentsWidget />
            <RecentActivity />
          </div>
        </div>
      </AdminLayout>
    </AdminGuard>
  );
} 