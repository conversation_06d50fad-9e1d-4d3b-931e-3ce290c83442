import { ProjectDetailClient } from './ProjectDetailClient';
import { Metadata } from 'next';

// Enable ISR (Incremental Static Regeneration)
export const revalidate = 3600; // Revalidate every hour

// For static export compatibility, provide empty static params
export async function generateStaticParams() {
  // Return empty array for static export, or fetch actual project IDs for ISR
  return [];
}

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const { id } = await params;
  return {
    title: `Project ${id} - Agnex One`,
    description: `Project details and management for ${id}`,
  };
}

export default async function ProjectDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  return <ProjectDetailClient projectId={id} />;
}
