'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useProjectManagement, ProjectManagementProvider } from '@/hooks/useProjectManagement';
import { useActivityLogger } from '@/hooks/useActivityLogger';
import { ProjectStatus, Project, ProjectRelationshipCheck } from '@/types/project';
import { ProjectCreationForm } from '@/components/admin/forms/ProjectCreationForm';
import { ProjectEditForm } from '@/components/admin/forms/ProjectEditForm';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { DeleteConfirmationDialog } from '@/components/admin/DeleteConfirmationDialog';
import { UnifiedHeader } from '@/components/admin/UnifiedHeader';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Search, 
  Filter,
  RefreshCw,
  Plus,
  FolderOpen,
  Calendar,
  CheckCircle,
  Clock,
  Pause,
  Play,
  FileText,
  Trash2
} from 'lucide-react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { Timestamp } from 'firebase/firestore';

function ProjectsPageContent() {
  const { user } = useAuth();
  const { logActivity } = useActivityLogger();
  const {
    projects,
    projectsLoading,
    projectFilters,
    setProjectFilters,
    loadProjects,
    updateProject,
    createProject,
    deleteProject,
    checkProjectRelationships,
  } = useProjectManagement();

  const [searchTerm, setSearchTerm] = useState('');
  const [updating, setUpdating] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [creating, setCreating] = useState(false);
  const [projectRelationships, setProjectRelationships] = useState<Record<string, ProjectRelationshipCheck>>({});

  // Load initial data
  useEffect(() => {
    loadProjects(true);
  }, [loadProjects]);

  // Load project relationships when projects change
  useEffect(() => {
    const loadRelationships = async () => {
      const relationshipPromises = projects.map(async (project) => {
        try {
          const relationships = await checkProjectRelationships(project.id);
          return { id: project.id, relationships };
        } catch (error) {
          console.error(`Error checking relationships for project ${project.id}:`, error);
          return { id: project.id, relationships: { canDelete: true, blockers: [], ticketCount: 0, paymentCount: 0 } };
        }
      });

      const results = await Promise.all(relationshipPromises);
      const relationshipMap = results.reduce((acc, { id, relationships }) => {
        acc[id] = relationships;
        return acc;
      }, {} as Record<string, ProjectRelationshipCheck>);

      setProjectRelationships(relationshipMap);
    };

    if (projects.length > 0) {
      loadRelationships();
    }
  }, [projects, checkProjectRelationships]);

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setProjectFilters({ ...projectFilters, searchTerm: value });
  };

  const handleCreateNew = () => {
    setCreateDialogOpen(true);
  };

  const handleCreateProject = async (data: { 
    name: string; 
    description: string; 
    totalCost: number;
    currency: string;
    status: 'draft' 
  }) => {
    if (!user) {
      toast.error('User not authenticated');
      return;
    }

    setCreating(true);
    try {
      const projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'> = {
        name: data.name,
        description: data.description,
        status: data.status,
        totalCost: data.totalCost,
        currency: data.currency,
        startDate: Timestamp.fromDate(new Date()),
      };

      await createProject(projectData);
      await logActivity('project_created', { projectName: data.name });
      
      toast.success('Project created successfully');
      setCreateDialogOpen(false);
    } catch (error) {
      console.error('Error creating project:', error);
      toast.error('Failed to create project');
    } finally {
      setCreating(false);
    }
  };

  const handleProjectFilterChange = (key: string, value: string) => {
    setProjectFilters({ ...projectFilters, [key]: value === 'all' ? undefined : value });
  };

  const handleProjectStatusUpdate = async (projectId: string, status: ProjectStatus) => {
    if (!user) return;
    
    setUpdating(projectId);
    try {
      await updateProject(projectId, { status });
      await logActivity('project_status_change', { projectId, newStatus: status });
      toast.success('Project status updated successfully');
    } catch (error) {
      console.error('Error updating project status:', error);
      toast.error('Failed to update project status');
    } finally {
      setUpdating(null);
    }
  };

  const handleViewProject = (project: Project) => {
    // Navigate to project detail page instead of dialog
    window.location.href = `/admin/projects/${project.id}`;
  };

  const handleEditProject = (project: Project) => {
    // Navigate to project detail page in edit mode instead of modal
    window.location.href = `/admin/projects/${project.id}?mode=edit`;
  };

  const handleDeleteProject = async (project: Project) => {
    try {
      await deleteProject(project.id);
      await logActivity('project_deleted', { projectId: project.id, projectName: project.name });
      toast.success('Project deleted successfully');
      
      // Remove from relationships cache
      setProjectRelationships(prev => {
        const updated = { ...prev };
        delete updated[project.id];
        return updated;
      });
    } catch (error: any) {
      console.error('Error deleting project:', error);
      toast.error(error.message || 'Failed to delete project');
    }
  };

  const formatDate = (timestamp: Timestamp | Date | null | undefined) => {
    if (!timestamp) return 'N/A';
    try {
      const date = timestamp instanceof Date ? timestamp : (timestamp as Timestamp).toDate();
      return format(date, 'MMM dd, yyyy');
    } catch {
      return 'Invalid date';
    }
  };

  const formatCurrency = (amount: number, currency = 'INR') => {
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount);
  };

  const getProjectStatusIcon = (status: ProjectStatus) => {
    switch (status) {
      case 'draft': return <FileText className="h-3 w-3" />;
      case 'active': return <Play className="h-3 w-3" />;
      case 'completed': return <CheckCircle className="h-3 w-3" />;
      case 'cancelled': return <Pause className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'draft':
        return 'outline';
      case 'active':
        return 'default';
      case 'completed':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <UnifiedHeader 
        title="Projects"
        subtitle="Manage your projects and track their progress"
        actions={
          <Button onClick={handleCreateNew}>
            <Plus className="mr-2 h-4 w-4" />
            New Project
          </Button>
        }
        stats={[
          { label: 'Total', value: projects.length, color: 'text-blue-600', icon: FolderOpen },
          { label: 'Active', value: projects.filter(p => p.status === 'active').length, color: 'text-green-600', icon: Play },
          { label: 'Draft', value: projects.filter(p => p.status === 'draft').length, color: 'text-yellow-600', icon: FileText },
          { label: 'Completed', value: projects.filter(p => p.status === 'completed').length, color: 'text-green-600', icon: CheckCircle },
        ]}
      />

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search projects..."
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select
                value={projectFilters.status || 'all'}
                onValueChange={(value) => handleProjectFilterChange('status', value)}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="icon"
                onClick={() => loadProjects(true)}
                disabled={projectsLoading}
              >
                <RefreshCw className={`h-4 w-4 ${projectsLoading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Projects Table */}
      <Card>
        <CardContent className="p-0">
          {projectsLoading && projects.length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <RefreshCw className="h-6 w-6 animate-spin" />
            </div>
          ) : projects.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
              <FolderOpen className="h-8 w-8 mb-2" />
              <p>No projects found</p>
              <p className="text-sm">Create your first project to get started</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Total Cost</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {projects.map((project) => (
                  <TableRow 
                    key={project.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleViewProject(project)}
                  >
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{project.name}</div>
                        <div className="text-sm text-muted-foreground truncate max-w-[300px]">
                          {project.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Select
                        value={project.status}
                        onValueChange={(value: ProjectStatus) => handleProjectStatusUpdate(project.id, value)}
                        disabled={updating === project.id}
                      >
                        <SelectTrigger 
                          className="w-auto h-8 p-1"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Badge variant={getStatusBadgeVariant(project.status)} className="gap-1">
                            {getProjectStatusIcon(project.status)}
                            {project.status}
                          </Badge>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      {formatCurrency(project.totalCost, project.currency)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        {formatDate(project.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2" onClick={(e) => e.stopPropagation()}>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewProject(project)}
                        >
                          <FolderOpen className="h-4 w-4 mr-1" />
                          Open
                        </Button>
                        <DeleteConfirmationDialog
                          title="Delete Project"
                          description="Are you sure you want to delete this project?"
                          itemName={project.name}
                          onConfirm={() => handleDeleteProject(project)}
                          relationships={projectRelationships[project.id]}
                        >
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </DeleteConfirmationDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create Project Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Project</DialogTitle>
            <DialogDescription>
              Create a new project with client information and requirements.
            </DialogDescription>
          </DialogHeader>
          <ProjectCreationForm
            onSubmit={handleCreateProject}
            onCancel={() => setCreateDialogOpen(false)}
            isLoading={creating}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default function ProjectsPage() {
  return (
    <ProjectManagementProvider>
      <AdminLayout>
        <ProjectsPageContent />
      </AdminLayout>
    </ProjectManagementProvider>
  );
} 