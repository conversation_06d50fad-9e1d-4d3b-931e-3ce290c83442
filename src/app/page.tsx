'use client';

import { useAuth } from '@/hooks/useAuth';
import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import Image from 'next/image';
import { Sparkles, Shield, Users, Zap } from 'lucide-react';

export default function HomePage() {
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading && user) {
      // Redirect authenticated users to dashboard
      window.location.href = '/dashboard';
    }
  }, [user, loading]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/20">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary/20 border-t-primary"></div>
      </div>
    );
  }

  if (user) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/10">
      {/* Navigation */}
      <header className="border-b border-border/50 bg-background/80 backdrop-blur-md sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Image
                src="/icon.png"
                alt="Agnex One Logo"
                width={32}
                height={32}
                className="w-8 h-8"
              />
              <div className="absolute inset-0 bg-primary/20 rounded-full blur-sm"></div>
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              Agnex One
            </h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <Badge variant="info">Professional Platform</Badge>
            <Button variant="gradient" size="sm" asChild>
              <Link href="/auth/login">Get Started</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center space-y-8">
          <div className="space-y-6">
            <div className="flex items-center justify-center space-x-2 mb-6">
              <Sparkles className="h-8 w-8 text-primary" />
              <h1 className="text-6xl font-bold bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent">
                Welcome to Agnex One
              </h1>
              <Sparkles className="h-8 w-8 text-primary" />
            </div>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Professional client management platform with enhanced styling, beautiful gradients, 
              and modern design. Experience the future of web applications.
            </p>
          </div>

          {/* Feature Cards */}
          <div className="grid md:grid-cols-3 gap-6 mt-16">
            <Card className="group hover:shadow-xl transition-all duration-300">
              <CardContent className="pt-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors duration-200">
                    <Shield className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Secure Authentication</h3>
                    <p className="text-sm text-muted-foreground">Role-based access control</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300">
              <CardContent className="pt-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-lg bg-success/10 group-hover:bg-success/20 transition-colors duration-200">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">User Management</h3>
                    <p className="text-sm text-muted-foreground">Complete user administration</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-xl transition-all duration-300">
              <CardContent className="pt-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-lg bg-info/10 group-hover:bg-info/20 transition-colors duration-200">
                    <Zap className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Modern UI</h3>
                    <p className="text-sm text-muted-foreground">Beautiful gradients & effects</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* CTA Section */}
          <div className="mt-16 space-y-6">
            <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl p-8 border border-border/50">
              <h2 className="text-2xl font-bold mb-4">Ready to Experience the Future?</h2>
              <p className="text-muted-foreground mb-6">
                Sign in to explore the enhanced dashboard with modern styling, beautiful gradients, 
                and improved user experience.
              </p>
              <Button variant="gradient" size="lg" asChild>
                <Link href="/auth/login">Sign In Now</Link>
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 