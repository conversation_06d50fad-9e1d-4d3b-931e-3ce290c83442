import { useState, useEffect } from 'react';
import { CompanyConfig } from '@/types/invoice';
import { doc, getDoc, setDoc, onSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export function useCompanyConfig() {
  const [companyConfig, setCompanyConfig] = useState<CompanyConfig | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Listen to company config changes
    const unsubscribe = onSnapshot(
      doc(db, 'companyConfigs', 'default'),
      (doc) => {
        if (doc.exists()) {
          setCompanyConfig(doc.data() as CompanyConfig);
        } else {
          // Create default config if it doesn't exist
          createDefaultConfig();
        }
        setLoading(false);
      },
      (error) => {
        console.error('Error loading company config:', error);
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, []);

  const createDefaultConfig = async () => {
    const defaultConfig: Omit<CompanyConfig, 'id' | 'createdAt' | 'updatedAt'> = {
      name: 'Your Company Name',
      address: {
        street: '123 Business Street',
        city: 'Business City',
        state: 'State',
        postalCode: '12345',
        country: 'Country'
      },
      contact: {
        email: '<EMAIL>',
        phone: '+****************'
      },
      business: {
        taxId: 'TAX123456789',
        registrationNumber: 'REG123456789'
      },
      invoiceSettings: {
        defaultTerms: 'Payment due within 30 days of invoice date.',
        defaultDueDays: 30,
        invoicePrefix: 'INV',
        startingNumber: 1
      }
    };

    try {
      await setDoc(doc(db, 'companyConfigs', 'default'), {
        ...defaultConfig,
        id: 'default',
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error creating default config:', error);
    }
  };

  const updateConfig = async (updates: Partial<CompanyConfig>) => {
    try {
      await setDoc(doc(db, 'companyConfigs', 'default'), {
        ...companyConfig,
        ...updates,
        updatedAt: new Date()
      }, { merge: true });
    } catch (error) {
      console.error('Error updating company config:', error);
      throw error;
    }
  };

  return {
    companyConfig,
    loading,
    updateConfig
  };
}
