'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { httpsCallable } from 'firebase/functions';
import { collection, doc, onSnapshot, query, where, orderBy } from 'firebase/firestore';
import { functions, db } from '@/lib/firebase';
import { ProjectFinancialSummary, Payment } from '@/types/project';

export function useProjectFinancials(projectId: string) {
  const [financialSummary, setFinancialSummary] = useState<ProjectFinancialSummary | null>(null);
  const [projectPayments, setProjectPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);

  // Cloud function references - use useMemo to avoid re-creation
  const calculateFinancials = useMemo(() => httpsCallable(functions, 'calculateProjectFinancials'), []);
  const updatePaymentStatusFn = useMemo(() => httpsCallable(functions, 'updatePaymentStatus'), []);

  // Load financial summary
  const loadFinancialSummary = useCallback(async (forceRecalculate = false) => {
    if (!projectId) return;
    
    setCalculating(true);
    try {
      const result = await calculateFinancials({ 
        projectId, 
        forceRecalculate
      });
      
      const data = result.data as { cached: boolean; summary: ProjectFinancialSummary };
      setFinancialSummary(data.summary);
    } catch (error) {
      console.error('Error loading financial summary:', error);
    } finally {
      setCalculating(false);
    }
  }, [projectId, calculateFinancials]);

  // Update payment status using Cloud Function
  const updatePaymentStatus = useCallback(async (
    paymentId: string, 
    newStatus: 'pending' | 'paid' | 'overdue' | 'cancelled',
    reason?: string
  ) => {
    try {
      setCalculating(true);
      await updatePaymentStatusFn({ paymentId, newStatus, reason });
      
      // Financial summary will be updated automatically via triggers
    } catch (error) {
      console.error('Error updating payment status:', error);
      throw error;
    } finally {
      setCalculating(false);
    }
  }, [updatePaymentStatusFn]);

  // Real-time listeners
  useEffect(() => {
    if (!projectId) return;

    // Listen to financial summary changes
    const summaryUnsub = onSnapshot(
      doc(db, 'projectFinancialSummaries', projectId),
      (doc) => {
        if (doc.exists()) {
          setFinancialSummary(doc.data() as ProjectFinancialSummary);
        }
      },
      (error) => {
        console.error('Error listening to financial summary:', error);
      }
    );

    // Listen to project payments
    const paymentsUnsub = onSnapshot(
      query(
        collection(db, 'payments'),
        where('projectId', '==', projectId),
        orderBy('createdAt', 'desc')
      ),
      (snapshot) => {
        const payments = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Payment[];
        setProjectPayments(payments);
      },
      (error) => {
        console.error('Error listening to payments:', error);
      }
    );

    // Listen to recurring payments for this project
    const recurringPaymentsUnsub = onSnapshot(
      query(
        collection(db, 'recurringPayments'),
        where('projectId', '==', projectId),
        orderBy('createdAt', 'desc')
      ),
      (snapshot) => {
        // Recurring payments changes should trigger financial recalculation
        // The financial summary will be updated automatically by the cloud function
        console.log('Recurring payments updated for project:', projectId);
      },
      (error) => {
        console.error('Error listening to recurring payments:', error);
      }
    );

    return () => {
      summaryUnsub();
      paymentsUnsub();
      recurringPaymentsUnsub();
    };
  }, [projectId]);

  // Initial load
  useEffect(() => {
    if (projectId) {
      setLoading(true);
      loadFinancialSummary().finally(() => setLoading(false));
    }
  }, [projectId, loadFinancialSummary]);

  // Simplified refresh function
  const refreshFinancials = useCallback(() => loadFinancialSummary(true), [loadFinancialSummary]);

  return {
    financialSummary,
    projectPayments,
    loading,
    calculating,
    loadFinancialSummary,
    updatePaymentStatus,
    refreshFinancials
  };
} 