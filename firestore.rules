rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read/write most collections
    match /projects/{projectId} {
      allow read, write: if request.auth != null;
    }
    
    match /payments/{paymentId} {
      allow read, write: if request.auth != null;
    }
    
    match /tickets/{ticketId} {
      allow read, write: if request.auth != null;
    }
    
    match /projectClients/{docId} {
      allow read, write: if request.auth != null;
    }
    
    match /projectFinancialSummaries/{projectId} {
      allow read, write: if request.auth != null;
    }
    
    // Invoice access control
    match /invoices/{invoiceId} {
      allow read, write: if request.auth != null;
    }

    // Recurring payment collections
    match /maintenanceContracts/{contractId} {
      allow read, write: if request.auth != null;
    }

    match /recurringPayments/{paymentId} {
      allow read, write: if request.auth != null;
    }

    match /paymentNotifications/{notificationId} {
      allow read, write: if request.auth != null;
    }

    match /scheduledProcessingLogs/{logId} {
      allow read: if request.auth != null && request.auth.token.role == 'admin';
      allow write: if false; // Only system can write logs
    }
    
    // Company config - admin only (for now, anyone authenticated)
    match /companyConfigs/{configId} {
      allow read, write: if request.auth != null;
    }
    
    // User and role management
    match /users/{userId} {
      allow read, write: if request.auth != null;
    }
    
    match /activities/{activityId} {
      allow read, write: if request.auth != null;
    }
    
    match /paymentStatusLogs/{logId} {
      allow read, write: if request.auth != null;
    }
    
    // Temporary dev access (remove after proper role-based rules)
    match /{document=**} {
      allow read, write: if request.time < timestamp.date(2025, 12, 31);
    }
  }
}