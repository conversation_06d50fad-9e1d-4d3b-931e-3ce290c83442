---

# Agnex One Project Structure

This is a full-stack application using Next.js for the frontend and Firebase Functions for the backend.

-   `src/`: Contains the frontend Next.js application. See [frontend-structure.mdc](mdc:.cursor/rules/frontend-structure.mdc) for more details.
-   `functions/`: Contains the backend Firebase Cloud Functions written in TypeScript. See [backend-structure.mdc](mdc:.cursor/rules/backend-structure.mdc) for more details.
-   `public/`: Static assets for the Next.js application.
-   `scripts/`: Node.js scripts for administrative tasks.
-   `firebase.json`: Configuration for Firebase services, including Functions and Hosting.
-   `firestore.rules`: Security rules for Firestore.

description:
globs:
alwaysApply: false
---
