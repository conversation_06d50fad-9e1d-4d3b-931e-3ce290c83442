# Recurring Payment System Documentation

## Overview

The Recurring Payment System is a comprehensive solution for managing maintenance contracts and their associated recurring payments. It integrates seamlessly with the existing project management system to provide automated payment tracking, notifications, and financial reporting.

## Features

### Core Functionality
- **Maintenance Contracts**: Create and manage recurring payment contracts
- **Automated Payment Generation**: Automatically create payment instances based on contract schedules
- **Payment Status Tracking**: Track pending, paid, overdue, and cancelled payments
- **Supporting Documents**: Upload and manage payment receipts and confirmations
- **Notifications**: Automated reminders and overdue notifications
- **Financial Integration**: Seamless integration with existing project financial tracking

### Payment Frequencies
- **Monthly**: Payments due every month
- **Quarterly**: Payments due every 3 months
- **Yearly**: Payments due annually

## Architecture

### Database Collections

#### maintenanceContracts
```typescript
{
  id: string;
  projectId: string;
  clientId: string;
  name: string;
  description?: string;
  amount: number;
  currency: string;
  frequency: 'monthly' | 'quarterly' | 'yearly';
  status: 'active' | 'paused' | 'cancelled' | 'expired';
  startDate: Timestamp;
  endDate?: Timestamp;
  nextDueDate: Timestamp;
  lastPaymentDate?: Timestamp;
  totalPaymentsMade: number;
  totalAmountPaid: number;
  autoGenerateInvoices: boolean;
  reminderDaysBefore: number;
  gracePeriodDays: number;
  notes?: string;
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### recurringPayments
```typescript
{
  id: string;
  maintenanceContractId: string;
  projectId: string;
  clientId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  dueDate: Timestamp;
  paidAt?: Timestamp;
  overdueAt?: Timestamp;
  description?: string;
  notes?: string;
  supportingDocuments?: SupportingDocument[];
  invoiceId?: string;
  invoicedAt?: Timestamp;
  reminderSentAt?: Timestamp;
  overdueNotificationSentAt?: Timestamp;
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### paymentNotifications
```typescript
{
  id: string;
  type: 'payment_due' | 'payment_overdue' | 'contract_expiring' | 'payment_received';
  title: string;
  message: string;
  recipientId: string;
  recipientEmail: string;
  relatedEntityId: string;
  relatedEntityType: 'maintenance_contract' | 'recurring_payment';
  isRead: boolean;
  sentAt?: Timestamp;
  createdAt: Timestamp;
}
```

### Cloud Functions

#### Contract Management
- `createMaintenanceContract`: Create new maintenance contracts
- `updateMaintenanceContract`: Update existing contracts
- `deleteMaintenanceContract`: Delete contracts and related payments

#### Payment Processing
- `markRecurringPaymentAsPaid`: Mark payments as paid with optional documents
- `markRecurringPaymentAsOverdue`: Mark payments as overdue
- `uploadSupportingDocument`: Upload payment confirmation documents
- `deleteSupportingDocument`: Remove uploaded documents

#### Scheduled Processing
- `processScheduledPayments`: Daily scheduled function to:
  - Create new payments for active contracts
  - Mark overdue payments
  - Send payment reminders
- `triggerPaymentProcessing`: Manual trigger for payment processing

#### Data Retrieval
- `getMaintenanceContracts`: Retrieve contracts with filtering and pagination
- `getRecurringPayments`: Retrieve payments with filtering and pagination
- `getRecurringPaymentSummary`: Get dashboard summary statistics
- `getUpcomingPayments`: Get payments due in specified timeframe
- `getOverduePayments`: Get all overdue payments

#### Notifications
- `getNotifications`: Retrieve user notifications
- `markNotificationAsRead`: Mark notifications as read
- `createNotification`: Create new notifications (admin only)

### Frontend Components

#### Admin Interface
- **RecurringPaymentsDashboard**: Main dashboard with overview and quick actions
- **MaintenanceContractsList**: List and manage all contracts
- **RecurringPaymentsList**: List and manage all payment instances
- **MaintenanceContractForm**: Create/edit maintenance contracts
- **FileUploadModal**: Upload supporting documents

#### Dashboard Widgets
- **RecurringPaymentsWidget**: Admin dashboard widget showing key metrics

## Usage

### Creating a Maintenance Contract

1. Navigate to Admin → Recurring Payments
2. Click "New Contract"
3. Fill in contract details:
   - Select project and client
   - Set contract name and description
   - Configure payment amount and frequency
   - Set start/end dates
   - Configure reminder and grace period settings
4. Save the contract

The system will automatically:
- Create the first payment instance
- Set up the payment schedule
- Begin monitoring for due dates

### Managing Payments

#### Marking Payments as Paid
1. Go to the Payments tab
2. Find the payment to mark as paid
3. Click "Mark as Paid" from the actions menu
4. Optionally upload supporting documents
5. Confirm the action

#### Uploading Supporting Documents
1. Navigate to a specific payment
2. Click "Upload Document" from the actions menu
3. Select files (PDF, images, documents)
4. Add optional description
5. Upload files

### Notifications

The system automatically sends notifications for:
- **Payment Due**: 3 days before due date (configurable)
- **Payment Overdue**: When grace period expires
- **Contract Expiring**: When contract end date approaches

## Configuration

### Environment Variables
```bash
# Email service configuration (optional)
SENDGRID_API_KEY=your_sendgrid_key
EMAIL_FROM=<EMAIL>

# Storage configuration
FIREBASE_STORAGE_BUCKET=your_storage_bucket
```

### Firestore Security Rules
```javascript
// Maintenance contracts
match /maintenanceContracts/{contractId} {
  allow read, write: if request.auth != null;
}

// Recurring payments
match /recurringPayments/{paymentId} {
  allow read, write: if request.auth != null;
}

// Payment notifications
match /paymentNotifications/{notificationId} {
  allow read, write: if request.auth != null;
}
```

## Scheduled Tasks

### Daily Payment Processing
The system runs a daily scheduled function at 9 AM UTC that:

1. **Checks for Overdue Payments**
   - Finds payments past their grace period
   - Marks them as overdue
   - Sends overdue notifications

2. **Creates New Payments**
   - Finds contracts with due dates today or in the past
   - Creates new payment instances
   - Updates contract next due dates

3. **Sends Reminders**
   - Finds payments due within reminder period
   - Sends reminder notifications
   - Marks reminders as sent

### Manual Processing
Admins can manually trigger payment processing:
```typescript
const triggerPaymentProcessing = httpsCallable(functions, 'triggerPaymentProcessing');
await triggerPaymentProcessing({ dryRun: false });
```

## Integration

### Project Financial System
Recurring payments are automatically included in:
- Project financial summaries
- Payment breakdowns
- Revenue calculations
- Progress tracking

### Invoice Generation
When `autoGenerateInvoices` is enabled:
- Invoices are automatically generated for new payments
- Invoice IDs are linked to payment records
- Invoice status is tracked

## Testing

### Running Tests
```bash
cd functions
npm test -- --testPathPattern=recurringPayments.test.ts
```

### Test Coverage
- Contract CRUD operations
- Payment processing workflows
- Scheduled payment processing
- Data validation
- Error handling
- Date calculations

## Monitoring

### Key Metrics to Monitor
- Active contracts count
- Monthly recurring revenue
- Payment success rate
- Overdue payment count
- Notification delivery rate

### Logs to Watch
- Payment processing errors
- Failed notification deliveries
- File upload failures
- Authentication errors

## Troubleshooting

### Common Issues

#### Payments Not Being Created
- Check contract status (must be 'active')
- Verify nextDueDate is not in the future
- Check scheduled function logs

#### Notifications Not Sending
- Verify user email addresses
- Check notification trigger logs
- Confirm email service configuration

#### File Upload Failures
- Check file size limits
- Verify file type restrictions
- Check Firebase Storage permissions

### Support
For technical support or questions about the recurring payment system, please contact the development team or refer to the main project documentation.
