# AGNEX Icon & Branding Setup

This document describes the icon and branding setup for the AGNEX One project management platform.

## Icon Files

The following icon files have been generated and are located in the `/public` directory:

### Favicon Files
- `favicon.ico` - Multi-size ICO file for browsers
- `favicon-16x16.png` - 16x16 favicon
- `favicon-32x32.png` - 32x32 favicon

### App Icons
- `apple-touch-icon.png` - 180x180 Apple touch icon
- `icon-192x192.png` - 192x192 PWA icon
- `icon-512x512.png` - 512x512 PWA icon
- `icon.png` - Base logo (512x512)

### Source Files
- `agnex_black.png` - Original AGNEX logo source file

## Design Specifications

### Logo Design
- **Text**: "AGNEX" in bold, clean font
- **Background**: Black (#0a0a0a)
- **Text Color**: White (#ffffff)
- **Style**: Modern, minimalist typography

### Color Scheme (Stone Theme)
- **Primary Background**: #0a0a0a (very dark)
- **Theme Color**: #1c1917 (stone dark)
- **Text**: White for contrast

## Implementation

### Next.js Layout Configuration
The icons are properly configured in `src/app/layout.tsx` with:
- Multiple favicon sizes
- Apple touch icon
- PWA manifest support

### PWA Manifest
Updated `public/manifest.json` includes:
- All required icon sizes
- Proper theme colors
- App metadata

### Browser Support
- ✅ Chrome/Edge (favicon.ico, PNG icons)
- ✅ Firefox (favicon.ico, PNG icons)
- ✅ Safari (apple-touch-icon.png)
- ✅ PWA (manifest icons)

## Regenerating Icons

If you need to regenerate the icons:

1. Update the source logo in `public/agnex_black.png`
2. Run the generation script:
   ```bash
   cd scripts
   ./generate-favicons.sh
   ```

Or use the HTML generator:
```bash
open scripts/generate-favicons.html
```

## Files Updated

1. ✅ `public/manifest.json` - Updated with new icons and theme colors
2. ✅ `src/app/layout.tsx` - Already configured for all icon sizes
3. ✅ Generated all required favicon files
4. ✅ Applied Stone theme colors throughout

## Theme Integration

The icon system now fully integrates with the Stone theme:
- Dark background matches the dark mode colors
- Clean typography reflects the professional aesthetic
- Color scheme coordinates with the CSS variables

---

*Generated on: June 30, 2025*
