import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Remove output: 'export' for dynamic deployment with SSR support
  // output: 'export', // Uncomment this line for static export only
  trailingSlash: true,
  images: {
    unoptimized: true
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Warning: This allows production builds to successfully complete even if
    // your project has type errors.
    ignoreBuildErrors: true,
  },
  // Enable external packages for Firebase integration
  serverExternalPackages: ['firebase-admin'],
};

export default nextConfig;
