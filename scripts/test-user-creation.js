#!/usr/bin/env node

/**
 * Test script to verify user creation functionality
 */

const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const auth = admin.auth();
const db = admin.firestore();

async function testUserCreation() {
  try {
    console.log('🧪 Testing user creation functionality...\n');

    // Test creating a user directly (simulating what the createUser function does)
    const testEmail = '<EMAIL>';
    const testPassword = 'TestPass123!';
    const testName = 'Test User';
    const testRole = 'client';

    console.log(`📧 Creating test user: ${testEmail}`);

    // Check if test user already exists and delete if so
    try {
      const existingUser = await auth.getUserByEmail(testEmail);
      console.log(`🗑️ Deleting existing test user: ${existingUser.uid}`);
      await auth.deleteUser(existingUser.uid);
      await db.collection('users').doc(existingUser.uid).delete();
    } catch (error) {
      if (error.code !== 'auth/user-not-found') {
        throw error;
      }
    }

    // Create the test user
    const userRecord = await auth.createUser({
      email: testEmail,
      password: testPassword,
      displayName: testName,
      emailVerified: false,
    });

    console.log(`✅ Firebase Auth user created: ${userRecord.uid}`);

    // Set custom claims
    await auth.setCustomUserClaims(userRecord.uid, { role: testRole });
    console.log(`✅ Custom claims set for user: ${userRecord.uid}`);

    // Create user document in Firestore
    await db.collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      email: testEmail,
      displayName: testName,
      role: testRole,
      isActive: true,
      emailVerified: false,
      profileComplete: false,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      createdBy: 'test-script',
    });

    console.log(`✅ Firestore user document created for: ${userRecord.uid}`);

    // Verify the user document exists
    const userDoc = await db.collection('users').doc(userRecord.uid).get();
    if (userDoc.exists) {
      const userData = userDoc.data();
      console.log(`✅ User document verified:`, {
        uid: userData.uid,
        email: userData.email,
        role: userData.role,
        isActive: userData.isActive
      });
    } else {
      throw new Error('User document not found after creation');
    }

    // Verify custom claims
    const updatedUser = await auth.getUser(userRecord.uid);
    const claims = updatedUser.customClaims;
    console.log(`✅ Custom claims verified:`, claims);

    console.log(`\n🎉 User creation test completed successfully!`);
    console.log(`📧 Test user email: ${testEmail}`);
    console.log(`🔑 Test user password: ${testPassword}`);
    console.log(`🆔 Test user UID: ${userRecord.uid}`);

    // Clean up test user
    console.log(`\n🧹 Cleaning up test user...`);
    await auth.deleteUser(userRecord.uid);
    await db.collection('users').doc(userRecord.uid).delete();
    console.log(`✅ Test user cleaned up`);

    return userRecord.uid;

  } catch (error) {
    console.error('❌ Error in user creation test:', error);
    throw error;
  }
}

// Run the test
testUserCreation()
  .then((uid) => {
    console.log(`\n✅ Test complete. All user creation functionality working correctly!`);
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }); 