#!/usr/bin/env node

/**
 * Bootstrap script to create the first admin user
 * This bypasses the normal createUser function to create the initial admin
 */

const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const auth = admin.auth();
const db = admin.firestore();

const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'AdminPass123!';
const ADMIN_NAME = 'System Administrator';

async function createBootstrapAdmin() {
  try {
    console.log('🚀 Creating bootstrap admin user...\n');

    // Check if user already exists
    try {
      const existingUser = await auth.getUserByEmail(ADMIN_EMAIL);
      console.log(`✅ User ${ADMIN_EMAIL} already exists with UID: ${existingUser.uid}`);
      
      // Update their role to admin
      await auth.setCustomUserClaims(existingUser.uid, { role: 'admin' });
      
      // Update/create user document
      await db.collection('users').doc(existingUser.uid).set({
        uid: existingUser.uid,
        email: ADMIN_EMAIL,
        displayName: existingUser.displayName || ADMIN_NAME,
        role: 'admin',
        isActive: true,
        emailVerified: true,
        profileComplete: true,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        createdBy: 'bootstrap-script',
      }, { merge: true });

      console.log(`✅ Updated ${ADMIN_EMAIL} to admin role`);
      return existingUser.uid;
    } catch (error) {
      if (error.code !== 'auth/user-not-found') {
        throw error;
      }
    }

    // Create new user
    console.log(`📧 Creating new admin user: ${ADMIN_EMAIL}`);
    
    const userRecord = await auth.createUser({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
      displayName: ADMIN_NAME,
      emailVerified: true,
    });

    console.log(`✅ Firebase Auth user created: ${userRecord.uid}`);

    // Set admin role
    await auth.setCustomUserClaims(userRecord.uid, { role: 'admin' });
    console.log(`✅ Admin role set for user: ${userRecord.uid}`);

    // Create user document
    await db.collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      email: ADMIN_EMAIL,
      displayName: ADMIN_NAME,
      role: 'admin',
      isActive: true,
      emailVerified: true,
      profileComplete: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      createdBy: 'bootstrap-script',
    });

    console.log(`✅ Firestore user document created for: ${userRecord.uid}`);

    // Create audit log
    await db.collection('audit_logs').add({
      adminId: 'bootstrap-script',
      adminEmail: 'system',
      action: 'bootstrap_admin_created',
      targetUserId: userRecord.uid,
      targetUserEmail: ADMIN_EMAIL,
      details: {
        role: 'admin',
        displayName: ADMIN_NAME,
        isActive: true,
        method: 'bootstrap',
      },
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });

    console.log(`\n🎉 Bootstrap admin created successfully!`);
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`🔑 Password: ${ADMIN_PASSWORD}`);
    console.log(`🆔 UID: ${userRecord.uid}`);
    console.log(`\n⚠️  IMPORTANT: Change the password after first login!`);

    return userRecord.uid;

  } catch (error) {
    console.error('❌ Error creating bootstrap admin:', error);
    throw error;
  }
}

// Run the bootstrap
createBootstrapAdmin()
  .then((uid) => {
    console.log(`\n✅ Bootstrap complete. Admin UID: ${uid}`);
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Bootstrap failed:', error);
    process.exit(1);
  }); 